import * as React from 'react';
import type { CommonProps } from '../../../box/common_props/common_props';
import type { Space } from '../../../metrics/metrics';
import type { ResponsiveValue } from '../../../responsive/responsive';
export declare const alignXs: readonly ["start", "center", "end", "spaceBetween"];
export type InlineAlignX = (typeof alignXs)[number];
export declare const alignYs: readonly ["start", "center", "end", "stretch"];
export type InlineAlignY = (typeof alignYs)[number];
export declare const breakpoints: readonly ["small", "medium", "large", "xLarge"];
type Breakpoint = (typeof breakpoints)[number];
export type InlineProps = {
    spacing: ResponsiveValue<Space>
    align?: ResponsiveValue<InlineAlignX>
    alignY?: ResponsiveValue<InlineAlignY>
    collapseBelow?: Breakpoint
    collapsed?: boolean
} & CommonProps;
export declare const Inline: React.ForwardRefExoticComponent<{
    spacing: ResponsiveValue<Space>
    align?: ResponsiveValue<InlineAlignX>
    alignY?: ResponsiveValue<InlineAlignY>
    collapseBelow?: Breakpoint
    collapsed?: boolean
} & CommonProps & {
    children?: React.ReactNode | undefined;
} & React.RefAttributes<HTMLElement>>;
export {};
