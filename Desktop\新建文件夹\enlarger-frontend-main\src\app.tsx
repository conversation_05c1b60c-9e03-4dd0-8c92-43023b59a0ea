import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Alert,
  FormField,
  Title,
  Box,
  ReloadIcon,
  Badge,
  Slider,
  SegmentedControl,
  ProgressBar,
  FileInput,
  FileInputItem,
} from "@canva/app-ui-kit";
import { useEffect, useState, useRef } from "react";
import type {
  ContentDraft,
  ImageRef,
  ImageElementAtPoint
} from "@canva/design";
import { addElementAtPoint, selection } from "@canva/design";
import { useMutation } from "@tanstack/react-query";
import styles from "styles/components.css";
import type { ImageMimeType } from "@canva/asset";
import { getTemporaryUrl, upload } from "@canva/asset";

// Mirror flip positions
type MirrorPosition = "right" | "left" | "below" | "above";

// Utility function to convert file to data URL
async function fileToDataUrl(file: Blob): Promise<string> {
  return new Promise<string>((resolve) => {
    const reader = new FileReader();
    reader.onload = () => {
      resolve(reader.result as string);
    };
    reader.readAsDataURL(file);
  });
}

// Read Canva native image URL
async function readCanvaNativeImageURL(url: string): Promise<File> {
  const res = await fetch(url);
  const formatMatch = url.match(/format:([A-Z]+)/);
  const ext = formatMatch ? formatMatch[1].toLowerCase() : "png";
  return new File([await res.blob()], `selected-image.${ext}`, {
    type: `image/${ext}`,
  });
}

// Transform image with mirror effect
async function transformImageWithMirror(
  ref: ImageRef,
  position: MirrorPosition,
  offset: number,
  opacity: number
): Promise<{ dataUrl: string; mimeType: ImageMimeType }> {
  // Get temporary URL for the asset
  const { url } = await getTemporaryUrl({
    type: "image",
    ref,
  });

  // Download the image
  const response = await fetch(url, { mode: "cors" });
  const imageBlob = await response.blob();
  const mimeType = imageBlob.type as ImageMimeType;

  // Create object URL for the image
  const objectURL = URL.createObjectURL(imageBlob);

  // Load image
  const image = new Image();
  image.crossOrigin = "Anonymous";
  await new Promise((resolve, reject) => {
    image.onload = resolve;
    image.onerror = () => reject(new Error("Image could not be loaded"));
    image.src = objectURL;
  });

  // Create canvas for the combined image
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  if (!ctx) {
    throw new Error("CanvasRenderingContext2D is not available");
  }

  // Calculate canvas dimensions based on position
  const originalWidth = image.width;
  const originalHeight = image.height;

  let canvasWidth = originalWidth;
  let canvasHeight = originalHeight;

  if (position === "right" || position === "left") {
    canvasWidth = originalWidth * 2 + offset;
  } else {
    canvasHeight = originalHeight * 2 + offset;
  }

  canvas.width = canvasWidth;
  canvas.height = canvasHeight;

  // Draw original image
  ctx.drawImage(image, 0, 0);

  // Set opacity for mirror
  ctx.globalAlpha = opacity / 100;

  // Draw mirror based on position
  ctx.save();

  switch (position) {
    case "right":
      ctx.translate(originalWidth + offset, 0);
      ctx.scale(-1, 1);
      ctx.drawImage(image, -originalWidth, 0);
      break;
    case "left":
      ctx.translate(0, 0);
      ctx.scale(-1, 1);
      ctx.drawImage(image, -originalWidth - offset, 0);
      break;
    case "below":
      ctx.translate(0, originalHeight + offset);
      ctx.scale(1, -1);
      ctx.drawImage(image, 0, -originalHeight);
      break;
    case "above":
      ctx.translate(0, 0);
      ctx.scale(1, -1);
      ctx.drawImage(image, 0, -originalHeight - offset);
      break;
  }

  ctx.restore();

  // Clean up
  URL.revokeObjectURL(objectURL);

  // Convert to data URL
  const dataUrl = canvas.toDataURL(mimeType);
  return { dataUrl, mimeType };
}

// Transform image with mirror effect from data URL
async function transformImageFromDataUrl(
  dataUrl: string,
  position: MirrorPosition,
  offset: number,
  opacity: number
): Promise<{ dataUrl: string; mimeType: ImageMimeType }> {
  // Load image from data URL
  const image = new Image();
  await new Promise((resolve, reject) => {
    image.onload = resolve;
    image.onerror = () => reject(new Error("Image could not be loaded"));
    image.src = dataUrl;
  });

  // Create canvas for the combined image
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  if (!ctx) {
    throw new Error("CanvasRenderingContext2D is not available");
  }

  // Calculate canvas dimensions based on position
  const originalWidth = image.width;
  const originalHeight = image.height;

  let canvasWidth = originalWidth;
  let canvasHeight = originalHeight;

  if (position === "right" || position === "left") {
    canvasWidth = originalWidth * 2 + offset;
  } else {
    canvasHeight = originalHeight * 2 + offset;
  }

  canvas.width = canvasWidth;
  canvas.height = canvasHeight;

  // Draw original image
  ctx.drawImage(image, 0, 0);

  // Set opacity for mirror
  ctx.globalAlpha = opacity / 100;

  // Draw mirror based on position
  ctx.save();

  switch (position) {
    case "right":
      ctx.translate(originalWidth + offset, 0);
      ctx.scale(-1, 1);
      ctx.drawImage(image, -originalWidth, 0);
      break;
    case "left":
      ctx.translate(0, 0);
      ctx.scale(-1, 1);
      ctx.drawImage(image, -originalWidth - offset, 0);
      break;
    case "below":
      ctx.translate(0, originalHeight + offset);
      ctx.scale(1, -1);
      ctx.drawImage(image, 0, -originalHeight);
      break;
    case "above":
      ctx.translate(0, 0);
      ctx.scale(1, -1);
      ctx.drawImage(image, 0, -originalHeight - offset);
      break;
  }

  ctx.restore();

  // Convert to data URL
  const mimeType = 'image/png' as ImageMimeType;
  const resultDataUrl = canvas.toDataURL(mimeType);
  return { dataUrl: resultDataUrl, mimeType };
}

export const App = () => {
  const [[file], setFiles] = useState<File[]>([]);
  const [imageSourceType, setImageSourceType] = useState<
    "upload" | "content" | "unknown"
  >("unknown");
  const [contentDraft, setContentDraft] = useState<ContentDraft<{
    ref: ImageRef;
  }> | null>(null);
  const [originImageURL, setOriginImageURL] = useState("");
  const [hasSelect, setHasSelect] = useState(false);

  // Mirror flip settings
  const [mirrorPosition, setMirrorPosition] = useState<MirrorPosition>("right");
  const [mirrorOffset, setMirrorOffset] = useState(10);
  const [mirrorOpacity, setMirrorOpacity] = useState(50);

  // Mirror flip processing mutation for selected images
  const {
    data: mirrorData,
    mutateAsync: processMirror,
    isPending: isProcessing,
    error: processError,
    reset: resetProcess,
  } = useMutation({
    mutationFn: async ({
      imageRef,
      position,
      offset,
      opacity,
    }: {
      imageRef: ImageRef;
      position: MirrorPosition;
      offset: number;
      opacity: number;
    }) => {
      const result = await transformImageWithMirror(imageRef, position, offset, opacity);
      return result;
    },
  });

  // Mirror flip processing mutation for uploaded files
  const {
    data: uploadMirrorData,
    mutateAsync: processUploadMirror,
    isPending: isUploadProcessing,
    error: uploadProcessError,
    reset: resetUploadProcess,
  } = useMutation({
    mutationFn: async ({
      dataUrl,
      position,
      offset,
      opacity,
    }: {
      dataUrl: string;
      position: MirrorPosition;
      offset: number;
      opacity: number;
    }) => {
      const result = await transformImageFromDataUrl(dataUrl, position, offset, opacity);
      return result;
    },
  });

  const mirrorUrl = mirrorData?.dataUrl || uploadMirrorData?.dataUrl;

  const combinedIsProcessing = isProcessing || isUploadProcessing;

  const stateRef = useRef({ imageSourceType, isProcessing: combinedIsProcessing, mirrorUrl });

  stateRef.current = {
    imageSourceType,
    isProcessing: combinedIsProcessing,
    mirrorUrl,
  };

  useEffect(() => {
    return selection.registerOnChange({
      scope: "image",
      async onChange(event) {
        const draft = await event.read();
        const ref = draft.contents[0]?.ref;
        setHasSelect(!!ref);
        const { imageSourceType, mirrorUrl, isProcessing } = stateRef.current;
        if (imageSourceType === "upload" || mirrorUrl || isProcessing) {
          return;
        }

        setContentDraft(draft);
        if (ref) {
          setImageSourceType("content");
          const { url } = await getTemporaryUrl({
            type: 'image',
            ref,
          });

          const file = await readCanvaNativeImageURL(url);
          setFiles([file]);
        } else if (imageSourceType === "content" && !isProcessing) {
          resetData();
        }
      },
    });
  }, []);

  useEffect(() => {
    if (!file || !FileReader) {
      return;
    }

    fileToDataUrl(file).then(setOriginImageURL);
  }, [file]);

  const {
    mutate: acceptImage,
    reset: resetAcceptImage,
    data: acceptResult,
    error: acceptError
  } = useMutation({
    mutationKey: [],
    mutationFn: async ({mirrorUrl, file, hasSelect}: {
      mirrorUrl: string,
      file: File,
      hasSelect: boolean
    }) => {
      if (
        contentDraft?.contents.length &&
        imageSourceType === "content" && hasSelect) {
        const asset = await upload({
          type: 'image',
          url: mirrorUrl,
          thumbnailUrl: mirrorUrl,
          mimeType: 'image/png' as ImageMimeType,
          parentRef: contentDraft.contents[0].ref,
          aiDisclosure: 'app_generated'
        });

        contentDraft.contents[0].ref = asset.ref;
        await contentDraft.save();
        return "replaced";
      } else {
        await addElementAtPoint({
          type: 'image',
          dataUrl: mirrorUrl,
        } as ImageElementAtPoint);
        return "added";
      }
    },
  });

  // Position options for mirror effect
  const positionOptions = [
    { value: "right", label: "Right" },
    { value: "left", label: "Left" },
    { value: "below", label: "Below" },
    { value: "above", label: "Above" },
  ];

  const resetData = () => {
    setFiles([]);
    setOriginImageURL("");
    resetProcess();
    resetUploadProcess();
    setImageSourceType("unknown");
    resetAcceptImage();
    setMirrorPosition("right");
    setMirrorOffset(10);
    setMirrorOpacity(50);
  };

  const isFileExceeded = file?.size > 1024 * 1024 * 5; // 5MB

  // Handle mirror processing
  const handleProcessMirror = async () => {
    if (!file) return;

    // If we have a content draft (selected image), use it
    if (contentDraft?.contents[0]?.ref) {
      await processMirror({
        imageRef: contentDraft.contents[0].ref,
        position: mirrorPosition,
        offset: mirrorOffset,
        opacity: mirrorOpacity,
      });
    } else if (imageSourceType === "upload" && file) {
      // For uploaded files, use the upload-specific processing function
      try {
        const dataUrl = await fileToDataUrl(file);

        // Process the mirror effect with the upload-specific function
        await processUploadMirror({
          dataUrl: dataUrl,
          position: mirrorPosition,
          offset: mirrorOffset,
          opacity: mirrorOpacity,
        });
      } catch (error) {
        console.error('Error processing mirror:', error);
        alert('镜像处理失败，请重试或使用其他图片');
      }
    }
  };

  if (combinedIsProcessing) {
    return (
      <Box
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        display="flex"
        className={styles.scrollContainer}
        paddingEnd="2u"
      >
        <Rows spacing="2u">
          <Title size="small" alignment="center">
            Creating mirror effect
          </Title>
          <ProgressBar value={75} />
          <Text alignment="center" size="small" tone="tertiary">
            Please wait, this should only take a few moments
          </Text>
          <Button onClick={resetData} variant="secondary">
            Cancel
          </Button>
        </Rows>
      </Box>
    );
  }

  return (
    <div className={styles.scrollContainer}>
      {mirrorUrl ? (
        <Rows spacing="2u">
          <>
            <Rows spacing="1u">
              {!!acceptResult && (
                <Alert tone="positive"
                onDismiss={resetAcceptImage}
                >
                  <Text variant="bold">
                    {acceptResult === "added"
                      ? "Image added to design"
                      : "Image replaced"}
                  </Text>
                </Alert>
              )}

              <Text variant="bold" size="medium">
                Mirror Effect Preview
              </Text>

              <div className={styles.imageCompareContainer}>
                {/* eslint-disable-next-line react/forbid-elements */}
                <img src={mirrorUrl} className={styles.originImage} alt="Mirror effect preview" />
              </div>
            </Rows>

            <Rows spacing="1u">
              <Button
                variant="primary"
                onClick={() => acceptImage({ mirrorUrl, file, hasSelect })}
              >
                {imageSourceType === "upload" || !hasSelect
                  ? "Add to design"
                  : "Replace"}
              </Button>
              <Button variant="secondary" onClick={resetData} icon={ReloadIcon}>
                Go back
              </Button>
            </Rows>
          </>
        </Rows>
      ) : (
        <Rows spacing="2u">
          <>
            <Title size="medium">Mirror Flip</Title>
            <Text>
              Select an image in your design to create a mirror effect with customizable position, offset, and opacity.
            </Text>

            <FormField
              description={
                originImageURL
                  ? ""
                  : "Select an image in your design to create a mirror effect"
              }
              label="Selected image"
              control={(props) =>
                originImageURL ? (
                  <>
                    {/* eslint-disable-next-line react/forbid-elements */}
                    <img src={originImageURL} className={styles.originImage} alt="Selected image" />

                    {imageSourceType === "upload" && (
                      <FileInputItem
                        onDeleteClick={() => {
                          resetData();
                        }}
                        label={file?.name}
                      />
                    )}
                  </>
                ) : (
                  <FileInput
                    {...props}
                    accept={[
                      "image/png",
                      "image/jpeg",
                      "image/jpg",
                      "image/webp",
                    ]}
                    stretchButton
                    onDropAcceptedFiles={(files) => {
                      setImageSourceType("upload");
                      setFiles(files);
                    }}
                  />
                )
              }
            />

            {!!file && (
              <>
                <FormField
                  label="Mirror Position"
                  control={(props) => (
                    <SegmentedControl
                      {...props}
                      value={mirrorPosition}
                      onChange={(value) => setMirrorPosition(value as MirrorPosition)}
                      options={positionOptions}
                    />
                  )}
                />

                <FormField
                  label={`Offset: ${mirrorOffset}px`}
                  control={(props) => (
                    <Slider
                      {...props}
                      min={0}
                      max={100}
                      value={mirrorOffset}
                      onChange={setMirrorOffset}
                    />
                  )}
                />

                <FormField
                  label={`Opacity: ${mirrorOpacity}%`}
                  control={(props) => (
                    <Slider
                      {...props}
                      min={0}
                      max={100}
                      value={mirrorOpacity}
                      onChange={setMirrorOpacity}
                    />
                  )}
                />

                <Button
                  stretch
                  variant="primary"
                  type="submit"
                  disabled={!file}
                  onClick={handleProcessMirror}
                >
                  Create Mirror Effect
                </Button>
              </>
            )}

            {processError && (
              <Alert tone="critical">{processError.message}</Alert>
            )}
          </>
        </Rows>
      )}
    </div>
  );
};
