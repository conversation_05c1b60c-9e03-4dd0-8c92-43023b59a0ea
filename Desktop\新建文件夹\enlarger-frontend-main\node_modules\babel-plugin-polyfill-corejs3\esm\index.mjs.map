{"version": 3, "file": "index.mjs", "sources": ["../src/shipped-proposals.ts", "../src/built-in-definitions.ts", "../src/usage-filters.ts", "../src/utils.ts", "../src/index.ts"], "sourcesContent": ["// This file is automatically generated by scripts/build-corejs3-shipped-proposals.mjs\n\nexport default new Set<string>([\n  \"esnext.array.from-async\",\n  \"esnext.array.group\",\n  \"esnext.array.group-to-map\",\n  \"esnext.array-buffer.detached\",\n  \"esnext.array-buffer.transfer\",\n  \"esnext.array-buffer.transfer-to-fixed-length\",\n  \"esnext.iterator.constructor\",\n  \"esnext.iterator.drop\",\n  \"esnext.iterator.every\",\n  \"esnext.iterator.filter\",\n  \"esnext.iterator.find\",\n  \"esnext.iterator.flat-map\",\n  \"esnext.iterator.for-each\",\n  \"esnext.iterator.from\",\n  \"esnext.iterator.map\",\n  \"esnext.iterator.reduce\",\n  \"esnext.iterator.some\",\n  \"esnext.iterator.take\",\n  \"esnext.iterator.to-array\",\n  \"esnext.json.is-raw-json\",\n  \"esnext.json.parse\",\n  \"esnext.json.raw-json\",\n  \"esnext.set.difference.v2\",\n  \"esnext.set.intersection.v2\",\n  \"esnext.set.is-disjoint-from.v2\",\n  \"esnext.set.is-subset-of.v2\",\n  \"esnext.set.is-superset-of.v2\",\n  \"esnext.set.symmetric-difference.v2\",\n  \"esnext.set.union.v2\",\n  \"esnext.symbol.async-dispose\",\n  \"esnext.symbol.dispose\",\n]);\n", "import corejs3Polyfills from \"../core-js-compat/data.js\";\n\ntype ObjectMap<V> = { [name: string]: V };\ntype ObjectMap2<V> = ObjectMap<ObjectMap<V>>;\n\nexport type CoreJSPolyfillDescriptor = {\n  name: string;\n  pure: string | null;\n  global: string[];\n  exclude: string[] | null;\n};\n\nconst polyfillsOrder = {};\nObject.keys(corejs3Polyfills).forEach((name, index) => {\n  polyfillsOrder[name] = index;\n});\n\nconst define = (\n  pure,\n  global,\n  name = global[0],\n  exclude?,\n): CoreJSPolyfillDescriptor => {\n  return {\n    name,\n    pure,\n    global: global.sort((a, b) => polyfillsOrder[a] - polyfillsOrder[b]),\n    exclude,\n  };\n};\n\nconst typed = (...modules) =>\n  define(null, [...modules, ...TypedArrayDependencies]);\n\nconst ArrayNatureIterators = [\n  \"es.array.iterator\",\n  \"web.dom-collections.iterator\",\n];\n\nexport const CommonIterators = [\"es.string.iterator\", ...ArrayNatureIterators];\n\nconst ArrayNatureIteratorsWithTag = [\n  \"es.object.to-string\",\n  ...ArrayNatureIterators,\n];\n\nconst CommonIteratorsWithTag = [\"es.object.to-string\", ...CommonIterators];\n\nconst ErrorDependencies = [\"es.error.cause\", \"es.error.to-string\"];\n\nconst SuppressedErrorDependencies = [\n  \"esnext.suppressed-error.constructor\",\n  ...ErrorDependencies,\n];\n\nconst TypedArrayDependencies = [\n  \"es.typed-array.at\",\n  \"es.typed-array.copy-within\",\n  \"es.typed-array.every\",\n  \"es.typed-array.fill\",\n  \"es.typed-array.filter\",\n  \"es.typed-array.find\",\n  \"es.typed-array.find-index\",\n  \"es.typed-array.find-last\",\n  \"es.typed-array.find-last-index\",\n  \"es.typed-array.for-each\",\n  \"es.typed-array.includes\",\n  \"es.typed-array.index-of\",\n  \"es.typed-array.iterator\",\n  \"es.typed-array.join\",\n  \"es.typed-array.last-index-of\",\n  \"es.typed-array.map\",\n  \"es.typed-array.reduce\",\n  \"es.typed-array.reduce-right\",\n  \"es.typed-array.reverse\",\n  \"es.typed-array.set\",\n  \"es.typed-array.slice\",\n  \"es.typed-array.some\",\n  \"es.typed-array.sort\",\n  \"es.typed-array.subarray\",\n  \"es.typed-array.to-locale-string\",\n  \"es.typed-array.to-reversed\",\n  \"es.typed-array.to-sorted\",\n  \"es.typed-array.to-string\",\n  \"es.typed-array.with\",\n  \"es.object.to-string\",\n  \"es.array.iterator\",\n  \"es.array-buffer.slice\",\n  \"esnext.array-buffer.detached\",\n  \"esnext.array-buffer.transfer\",\n  \"esnext.array-buffer.transfer-to-fixed-length\",\n  \"esnext.typed-array.filter-reject\",\n  \"esnext.typed-array.group-by\",\n  \"esnext.typed-array.to-spliced\",\n  \"esnext.typed-array.unique-by\",\n];\n\nexport const PromiseDependencies = [\"es.promise\", \"es.object.to-string\"];\n\nexport const PromiseDependenciesWithIterators = [\n  ...PromiseDependencies,\n  ...CommonIterators,\n];\n\nconst SymbolDependencies = [\n  \"es.symbol\",\n  \"es.symbol.description\",\n  \"es.object.to-string\",\n];\n\nconst MapDependencies = [\n  \"es.map\",\n  \"esnext.map.delete-all\",\n  \"esnext.map.emplace\",\n  \"esnext.map.every\",\n  \"esnext.map.filter\",\n  \"esnext.map.find\",\n  \"esnext.map.find-key\",\n  \"esnext.map.includes\",\n  \"esnext.map.key-of\",\n  \"esnext.map.map-keys\",\n  \"esnext.map.map-values\",\n  \"esnext.map.merge\",\n  \"esnext.map.reduce\",\n  \"esnext.map.some\",\n  \"esnext.map.update\",\n  ...CommonIteratorsWithTag,\n];\n\nconst SetDependencies = [\n  \"es.set\",\n  \"esnext.set.add-all\",\n  \"esnext.set.delete-all\",\n  \"esnext.set.difference\",\n  \"esnext.set.difference.v2\",\n  \"esnext.set.every\",\n  \"esnext.set.filter\",\n  \"esnext.set.find\",\n  \"esnext.set.intersection\",\n  \"esnext.set.intersection.v2\",\n  \"esnext.set.is-disjoint-from\",\n  \"esnext.set.is-disjoint-from.v2\",\n  \"esnext.set.is-subset-of\",\n  \"esnext.set.is-subset-of.v2\",\n  \"esnext.set.is-superset-of\",\n  \"esnext.set.is-superset-of.v2\",\n  \"esnext.set.join\",\n  \"esnext.set.map\",\n  \"esnext.set.reduce\",\n  \"esnext.set.some\",\n  \"esnext.set.symmetric-difference\",\n  \"esnext.set.symmetric-difference.v2\",\n  \"esnext.set.union\",\n  \"esnext.set.union.v2\",\n  ...CommonIteratorsWithTag,\n];\n\nconst WeakMapDependencies = [\n  \"es.weak-map\",\n  \"esnext.weak-map.delete-all\",\n  \"esnext.weak-map.emplace\",\n  ...CommonIteratorsWithTag,\n];\n\nconst WeakSetDependencies = [\n  \"es.weak-set\",\n  \"esnext.weak-set.add-all\",\n  \"esnext.weak-set.delete-all\",\n  ...CommonIteratorsWithTag,\n];\n\nconst DOMExceptionDependencies = [\n  \"web.dom-exception.constructor\",\n  \"web.dom-exception.stack\",\n  \"web.dom-exception.to-string-tag\",\n  \"es.error.to-string\",\n];\n\nconst URLSearchParamsDependencies = [\n  \"web.url-search-params\",\n  \"web.url-search-params.delete\",\n  \"web.url-search-params.has\",\n  \"web.url-search-params.size\",\n  ...CommonIteratorsWithTag,\n];\n\nconst AsyncIteratorDependencies = [\n  \"esnext.async-iterator.constructor\",\n  ...PromiseDependencies,\n];\n\nconst AsyncIteratorProblemMethods = [\n  \"esnext.async-iterator.every\",\n  \"esnext.async-iterator.filter\",\n  \"esnext.async-iterator.find\",\n  \"esnext.async-iterator.flat-map\",\n  \"esnext.async-iterator.for-each\",\n  \"esnext.async-iterator.map\",\n  \"esnext.async-iterator.reduce\",\n  \"esnext.async-iterator.some\",\n];\n\nconst IteratorDependencies = [\n  \"esnext.iterator.constructor\",\n  \"es.object.to-string\",\n];\n\nexport const DecoratorMetadataDependencies = [\n  \"esnext.symbol.metadata\",\n  \"esnext.function.metadata\",\n];\n\nconst TypedArrayStaticMethods = {\n  from: define(null, [\"es.typed-array.from\", ...TypedArrayDependencies]),\n  fromAsync: define(null, [\n    \"esnext.typed-array.from-async\",\n    ...PromiseDependenciesWithIterators,\n    ...TypedArrayDependencies,\n  ]),\n  of: define(null, [\"es.typed-array.of\", ...TypedArrayDependencies]),\n};\n\nconst DataViewDependencies = [\n  \"es.data-view\",\n  \"es.array-buffer.slice\",\n  \"es.object.to-string\",\n];\n\nexport const BuiltIns: ObjectMap<CoreJSPolyfillDescriptor> = {\n  AsyncDisposableStack: define(\"async-disposable-stack/index\", [\n    \"esnext.async-disposable-stack.constructor\",\n    \"es.object.to-string\",\n    \"esnext.async-iterator.async-dispose\",\n    \"esnext.iterator.dispose\",\n    ...PromiseDependencies,\n    ...SuppressedErrorDependencies,\n  ]),\n  AsyncIterator: define(\"async-iterator/index\", AsyncIteratorDependencies),\n  AggregateError: define(\"aggregate-error\", [\n    \"es.aggregate-error\",\n    ...ErrorDependencies,\n    ...CommonIteratorsWithTag,\n    \"es.aggregate-error.cause\",\n  ]),\n  ArrayBuffer: define(null, [\n    \"es.array-buffer.constructor\",\n    \"es.array-buffer.slice\",\n    \"es.object.to-string\",\n  ]),\n  DataView: define(null, DataViewDependencies),\n  Date: define(null, [\"es.date.to-string\"]),\n  DOMException: define(\"dom-exception/index\", DOMExceptionDependencies),\n  DisposableStack: define(\"disposable-stack/index\", [\n    \"esnext.disposable-stack.constructor\",\n    \"es.object.to-string\",\n    \"esnext.iterator.dispose\",\n    ...SuppressedErrorDependencies,\n  ]),\n  Error: define(null, ErrorDependencies),\n  EvalError: define(null, ErrorDependencies),\n  Float32Array: typed(\"es.typed-array.float32-array\"),\n  Float64Array: typed(\"es.typed-array.float64-array\"),\n  Int8Array: typed(\"es.typed-array.int8-array\"),\n  Int16Array: typed(\"es.typed-array.int16-array\"),\n  Int32Array: typed(\"es.typed-array.int32-array\"),\n  Iterator: define(\"iterator/index\", IteratorDependencies),\n  Uint8Array: typed(\n    \"es.typed-array.uint8-array\",\n    \"esnext.uint8-array.to-base64\",\n    \"esnext.uint8-array.to-hex\",\n  ),\n  Uint8ClampedArray: typed(\"es.typed-array.uint8-clamped-array\"),\n  Uint16Array: typed(\"es.typed-array.uint16-array\"),\n  Uint32Array: typed(\"es.typed-array.uint32-array\"),\n  Map: define(\"map/index\", MapDependencies),\n  Number: define(null, [\"es.number.constructor\"]),\n  Observable: define(\"observable/index\", [\n    \"esnext.observable\",\n    \"esnext.symbol.observable\",\n    \"es.object.to-string\",\n    ...CommonIteratorsWithTag,\n  ]),\n  Promise: define(\"promise/index\", PromiseDependencies),\n  RangeError: define(null, ErrorDependencies),\n  ReferenceError: define(null, ErrorDependencies),\n  Reflect: define(null, [\"es.reflect.to-string-tag\", \"es.object.to-string\"]),\n  RegExp: define(null, [\n    \"es.regexp.constructor\",\n    \"es.regexp.dot-all\",\n    \"es.regexp.exec\",\n    \"es.regexp.sticky\",\n    \"es.regexp.to-string\",\n  ]),\n  Set: define(\"set/index\", SetDependencies),\n  SuppressedError: define(\"suppressed-error\", SuppressedErrorDependencies),\n  Symbol: define(\"symbol/index\", SymbolDependencies),\n  SyntaxError: define(null, ErrorDependencies),\n  TypeError: define(null, ErrorDependencies),\n  URIError: define(null, ErrorDependencies),\n  URL: define(\"url/index\", [\"web.url\", ...URLSearchParamsDependencies]),\n  URLSearchParams: define(\n    \"url-search-params/index\",\n    URLSearchParamsDependencies,\n  ),\n  WeakMap: define(\"weak-map/index\", WeakMapDependencies),\n  WeakSet: define(\"weak-set/index\", WeakSetDependencies),\n\n  atob: define(\"atob\", [\"web.atob\", ...DOMExceptionDependencies]),\n  btoa: define(\"btoa\", [\"web.btoa\", ...DOMExceptionDependencies]),\n  clearImmediate: define(\"clear-immediate\", [\"web.immediate\"]),\n  compositeKey: define(\"composite-key\", [\"esnext.composite-key\"]),\n  compositeSymbol: define(\"composite-symbol\", [\"esnext.composite-symbol\"]),\n  escape: define(\"escape\", [\"es.escape\"]),\n  fetch: define(null, PromiseDependencies),\n  globalThis: define(\"global-this\", [\"es.global-this\"]),\n  parseFloat: define(\"parse-float\", [\"es.parse-float\"]),\n  parseInt: define(\"parse-int\", [\"es.parse-int\"]),\n  queueMicrotask: define(\"queue-microtask\", [\"web.queue-microtask\"]),\n  self: define(\"self\", [\"web.self\"]),\n  setImmediate: define(\"set-immediate\", [\"web.immediate\"]),\n  setInterval: define(\"set-interval\", [\"web.timers\"]),\n  setTimeout: define(\"set-timeout\", [\"web.timers\"]),\n  structuredClone: define(\"structured-clone\", [\n    \"web.structured-clone\",\n    ...DOMExceptionDependencies,\n    \"es.array.iterator\",\n    \"es.object.keys\",\n    \"es.object.to-string\",\n    \"es.map\",\n    \"es.set\",\n  ]),\n  unescape: define(\"unescape\", [\"es.unescape\"]),\n};\n\nexport const StaticProperties: ObjectMap2<CoreJSPolyfillDescriptor> = {\n  AsyncIterator: {\n    from: define(\"async-iterator/from\", [\n      \"esnext.async-iterator.from\",\n      ...AsyncIteratorDependencies,\n      ...AsyncIteratorProblemMethods,\n      ...CommonIterators,\n    ]),\n  },\n  Array: {\n    from: define(\"array/from\", [\"es.array.from\", \"es.string.iterator\"]),\n    fromAsync: define(\"array/from-async\", [\n      \"esnext.array.from-async\",\n      ...PromiseDependenciesWithIterators,\n    ]),\n    isArray: define(\"array/is-array\", [\"es.array.is-array\"]),\n    isTemplateObject: define(\"array/is-template-object\", [\n      \"esnext.array.is-template-object\",\n    ]),\n    of: define(\"array/of\", [\"es.array.of\"]),\n  },\n\n  ArrayBuffer: {\n    isView: define(null, [\"es.array-buffer.is-view\"]),\n  },\n\n  BigInt: {\n    range: define(\"bigint/range\", [\n      \"esnext.bigint.range\",\n      \"es.object.to-string\",\n    ]),\n  },\n\n  Date: {\n    now: define(\"date/now\", [\"es.date.now\"]),\n  },\n\n  Function: {\n    isCallable: define(\"function/is-callable\", [\"esnext.function.is-callable\"]),\n    isConstructor: define(\"function/is-constructor\", [\n      \"esnext.function.is-constructor\",\n    ]),\n  },\n\n  Iterator: {\n    from: define(\"iterator/from\", [\n      \"esnext.iterator.from\",\n      ...IteratorDependencies,\n      ...CommonIterators,\n    ]),\n    range: define(\"iterator/range\", [\n      \"esnext.iterator.range\",\n      \"es.object.to-string\",\n    ]),\n  },\n\n  JSON: {\n    isRawJSON: define(\"json/is-raw-json\", [\"esnext.json.is-raw-json\"]),\n    parse: define(\"json/parse\", [\"esnext.json.parse\", \"es.object.keys\"]),\n    rawJSON: define(\"json/raw-json\", [\n      \"esnext.json.raw-json\",\n      \"es.object.create\",\n      \"es.object.freeze\",\n    ]),\n    stringify: define(\"json/stringify\", [\"es.json.stringify\"], \"es.symbol\"),\n  },\n\n  Math: {\n    DEG_PER_RAD: define(\"math/deg-per-rad\", [\"esnext.math.deg-per-rad\"]),\n    RAD_PER_DEG: define(\"math/rad-per-deg\", [\"esnext.math.rad-per-deg\"]),\n    acosh: define(\"math/acosh\", [\"es.math.acosh\"]),\n    asinh: define(\"math/asinh\", [\"es.math.asinh\"]),\n    atanh: define(\"math/atanh\", [\"es.math.atanh\"]),\n    cbrt: define(\"math/cbrt\", [\"es.math.cbrt\"]),\n    clamp: define(\"math/clamp\", [\"esnext.math.clamp\"]),\n    clz32: define(\"math/clz32\", [\"es.math.clz32\"]),\n    cosh: define(\"math/cosh\", [\"es.math.cosh\"]),\n    degrees: define(\"math/degrees\", [\"esnext.math.degrees\"]),\n    expm1: define(\"math/expm1\", [\"es.math.expm1\"]),\n    fround: define(\"math/fround\", [\"es.math.fround\"]),\n    f16round: define(\"math/f16round\", [\"esnext.math.f16round\"]),\n    fscale: define(\"math/fscale\", [\"esnext.math.fscale\"]),\n    hypot: define(\"math/hypot\", [\"es.math.hypot\"]),\n    iaddh: define(\"math/iaddh\", [\"esnext.math.iaddh\"]),\n    imul: define(\"math/imul\", [\"es.math.imul\"]),\n    imulh: define(\"math/imulh\", [\"esnext.math.imulh\"]),\n    isubh: define(\"math/isubh\", [\"esnext.math.isubh\"]),\n    log10: define(\"math/log10\", [\"es.math.log10\"]),\n    log1p: define(\"math/log1p\", [\"es.math.log1p\"]),\n    log2: define(\"math/log2\", [\"es.math.log2\"]),\n    radians: define(\"math/radians\", [\"esnext.math.radians\"]),\n    scale: define(\"math/scale\", [\"esnext.math.scale\"]),\n    seededPRNG: define(\"math/seeded-prng\", [\"esnext.math.seeded-prng\"]),\n    sign: define(\"math/sign\", [\"es.math.sign\"]),\n    signbit: define(\"math/signbit\", [\"esnext.math.signbit\"]),\n    sinh: define(\"math/sinh\", [\"es.math.sinh\"]),\n    tanh: define(\"math/tanh\", [\"es.math.tanh\"]),\n    trunc: define(\"math/trunc\", [\"es.math.trunc\"]),\n    umulh: define(\"math/umulh\", [\"esnext.math.umulh\"]),\n  },\n\n  Map: {\n    from: define(null, [\"esnext.map.from\", ...MapDependencies]),\n    groupBy: define(null, [\"es.map.group-by\", ...MapDependencies]),\n    keyBy: define(null, [\"esnext.map.key-by\", ...MapDependencies]),\n    of: define(null, [\"esnext.map.of\", ...MapDependencies]),\n  },\n\n  Number: {\n    EPSILON: define(\"number/epsilon\", [\"es.number.epsilon\"]),\n    MAX_SAFE_INTEGER: define(\"number/max-safe-integer\", [\n      \"es.number.max-safe-integer\",\n    ]),\n    MIN_SAFE_INTEGER: define(\"number/min-safe-integer\", [\n      \"es.number.min-safe-integer\",\n    ]),\n    fromString: define(\"number/from-string\", [\"esnext.number.from-string\"]),\n    isFinite: define(\"number/is-finite\", [\"es.number.is-finite\"]),\n    isInteger: define(\"number/is-integer\", [\"es.number.is-integer\"]),\n    isNaN: define(\"number/is-nan\", [\"es.number.is-nan\"]),\n    isSafeInteger: define(\"number/is-safe-integer\", [\n      \"es.number.is-safe-integer\",\n    ]),\n    parseFloat: define(\"number/parse-float\", [\"es.number.parse-float\"]),\n    parseInt: define(\"number/parse-int\", [\"es.number.parse-int\"]),\n    range: define(\"number/range\", [\n      \"esnext.number.range\",\n      \"es.object.to-string\",\n    ]),\n  },\n\n  Object: {\n    assign: define(\"object/assign\", [\"es.object.assign\"]),\n    create: define(\"object/create\", [\"es.object.create\"]),\n    defineProperties: define(\"object/define-properties\", [\n      \"es.object.define-properties\",\n    ]),\n    defineProperty: define(\"object/define-property\", [\n      \"es.object.define-property\",\n    ]),\n    entries: define(\"object/entries\", [\"es.object.entries\"]),\n    freeze: define(\"object/freeze\", [\"es.object.freeze\"]),\n    fromEntries: define(\"object/from-entries\", [\n      \"es.object.from-entries\",\n      \"es.array.iterator\",\n    ]),\n    getOwnPropertyDescriptor: define(\"object/get-own-property-descriptor\", [\n      \"es.object.get-own-property-descriptor\",\n    ]),\n    getOwnPropertyDescriptors: define(\"object/get-own-property-descriptors\", [\n      \"es.object.get-own-property-descriptors\",\n    ]),\n    getOwnPropertyNames: define(\"object/get-own-property-names\", [\n      \"es.object.get-own-property-names\",\n    ]),\n    getOwnPropertySymbols: define(\"object/get-own-property-symbols\", [\n      \"es.symbol\",\n    ]),\n    getPrototypeOf: define(\"object/get-prototype-of\", [\n      \"es.object.get-prototype-of\",\n    ]),\n    groupBy: define(\"object/group-by\", [\n      \"es.object.group-by\",\n      \"es.object.create\",\n    ]),\n    hasOwn: define(\"object/has-own\", [\"es.object.has-own\"]),\n    is: define(\"object/is\", [\"es.object.is\"]),\n    isExtensible: define(\"object/is-extensible\", [\"es.object.is-extensible\"]),\n    isFrozen: define(\"object/is-frozen\", [\"es.object.is-frozen\"]),\n    isSealed: define(\"object/is-sealed\", [\"es.object.is-sealed\"]),\n    keys: define(\"object/keys\", [\"es.object.keys\"]),\n    preventExtensions: define(\"object/prevent-extensions\", [\n      \"es.object.prevent-extensions\",\n    ]),\n    seal: define(\"object/seal\", [\"es.object.seal\"]),\n    setPrototypeOf: define(\"object/set-prototype-of\", [\n      \"es.object.set-prototype-of\",\n    ]),\n    values: define(\"object/values\", [\"es.object.values\"]),\n  },\n\n  Promise: {\n    all: define(null, PromiseDependenciesWithIterators),\n    allSettled: define(null, [\n      \"es.promise.all-settled\",\n      ...PromiseDependenciesWithIterators,\n    ]),\n    any: define(null, [\n      \"es.promise.any\",\n      \"es.aggregate-error\",\n      ...PromiseDependenciesWithIterators,\n    ]),\n    race: define(null, PromiseDependenciesWithIterators),\n    try: define(null, [\"esnext.promise.try\", ...PromiseDependencies]),\n    withResolvers: define(null, [\n      \"es.promise.with-resolvers\",\n      ...PromiseDependencies,\n    ]),\n  },\n\n  Reflect: {\n    apply: define(\"reflect/apply\", [\"es.reflect.apply\"]),\n    construct: define(\"reflect/construct\", [\"es.reflect.construct\"]),\n    defineMetadata: define(\"reflect/define-metadata\", [\n      \"esnext.reflect.define-metadata\",\n    ]),\n    defineProperty: define(\"reflect/define-property\", [\n      \"es.reflect.define-property\",\n    ]),\n    deleteMetadata: define(\"reflect/delete-metadata\", [\n      \"esnext.reflect.delete-metadata\",\n    ]),\n    deleteProperty: define(\"reflect/delete-property\", [\n      \"es.reflect.delete-property\",\n    ]),\n    get: define(\"reflect/get\", [\"es.reflect.get\"]),\n    getMetadata: define(\"reflect/get-metadata\", [\n      \"esnext.reflect.get-metadata\",\n    ]),\n    getMetadataKeys: define(\"reflect/get-metadata-keys\", [\n      \"esnext.reflect.get-metadata-keys\",\n    ]),\n    getOwnMetadata: define(\"reflect/get-own-metadata\", [\n      \"esnext.reflect.get-own-metadata\",\n    ]),\n    getOwnMetadataKeys: define(\"reflect/get-own-metadata-keys\", [\n      \"esnext.reflect.get-own-metadata-keys\",\n    ]),\n    getOwnPropertyDescriptor: define(\"reflect/get-own-property-descriptor\", [\n      \"es.reflect.get-own-property-descriptor\",\n    ]),\n    getPrototypeOf: define(\"reflect/get-prototype-of\", [\n      \"es.reflect.get-prototype-of\",\n    ]),\n    has: define(\"reflect/has\", [\"es.reflect.has\"]),\n    hasMetadata: define(\"reflect/has-metadata\", [\n      \"esnext.reflect.has-metadata\",\n    ]),\n    hasOwnMetadata: define(\"reflect/has-own-metadata\", [\n      \"esnext.reflect.has-own-metadata\",\n    ]),\n    isExtensible: define(\"reflect/is-extensible\", [\"es.reflect.is-extensible\"]),\n    metadata: define(\"reflect/metadata\", [\"esnext.reflect.metadata\"]),\n    ownKeys: define(\"reflect/own-keys\", [\"es.reflect.own-keys\"]),\n    preventExtensions: define(\"reflect/prevent-extensions\", [\n      \"es.reflect.prevent-extensions\",\n    ]),\n    set: define(\"reflect/set\", [\"es.reflect.set\"]),\n    setPrototypeOf: define(\"reflect/set-prototype-of\", [\n      \"es.reflect.set-prototype-of\",\n    ]),\n  },\n\n  RegExp: {\n    escape: define(\"regexp/escape\", [\"esnext.regexp.escape\"]),\n  },\n\n  Set: {\n    from: define(null, [\"esnext.set.from\", ...SetDependencies]),\n    of: define(null, [\"esnext.set.of\", ...SetDependencies]),\n  },\n\n  String: {\n    cooked: define(\"string/cooked\", [\"esnext.string.cooked\"]),\n    dedent: define(\"string/dedent\", [\n      \"esnext.string.dedent\",\n      \"es.string.from-code-point\",\n      \"es.weak-map\",\n    ]),\n    fromCodePoint: define(\"string/from-code-point\", [\n      \"es.string.from-code-point\",\n    ]),\n    raw: define(\"string/raw\", [\"es.string.raw\"]),\n  },\n\n  Symbol: {\n    asyncDispose: define(\"symbol/async-dispose\", [\n      \"esnext.symbol.async-dispose\",\n      \"esnext.async-iterator.async-dispose\",\n    ]),\n    asyncIterator: define(\"symbol/async-iterator\", [\n      \"es.symbol.async-iterator\",\n    ]),\n    dispose: define(\"symbol/dispose\", [\n      \"esnext.symbol.dispose\",\n      \"esnext.iterator.dispose\",\n    ]),\n    for: define(\"symbol/for\", [], \"es.symbol\"),\n    hasInstance: define(\"symbol/has-instance\", [\n      \"es.symbol.has-instance\",\n      \"es.function.has-instance\",\n    ]),\n    isConcatSpreadable: define(\"symbol/is-concat-spreadable\", [\n      \"es.symbol.is-concat-spreadable\",\n      \"es.array.concat\",\n    ]),\n    isRegistered: define(\"symbol/is-registered\", [\n      \"esnext.symbol.is-registered\",\n      \"es.symbol\",\n    ]),\n    isRegisteredSymbol: define(\"symbol/is-registered-symbol\", [\n      \"esnext.symbol.is-registered-symbol\",\n      \"es.symbol\",\n    ]),\n    isWellKnown: define(\"symbol/is-well-known\", [\n      \"esnext.symbol.is-well-known\",\n      \"es.symbol\",\n    ]),\n    isWellKnownSymbol: define(\"symbol/is-well-known-symbol\", [\n      \"esnext.symbol.is-well-known-symbol\",\n      \"es.symbol\",\n    ]),\n    iterator: define(\"symbol/iterator\", [\n      \"es.symbol.iterator\",\n      ...CommonIteratorsWithTag,\n    ]),\n    keyFor: define(\"symbol/key-for\", [], \"es.symbol\"),\n    match: define(\"symbol/match\", [\"es.symbol.match\", \"es.string.match\"]),\n    matcher: define(\"symbol/matcher\", [\"esnext.symbol.matcher\"]),\n    matchAll: define(\"symbol/match-all\", [\n      \"es.symbol.match-all\",\n      \"es.string.match-all\",\n    ]),\n    metadata: define(\"symbol/metadata\", DecoratorMetadataDependencies),\n    metadataKey: define(\"symbol/metadata-key\", [\"esnext.symbol.metadata-key\"]),\n    observable: define(\"symbol/observable\", [\"esnext.symbol.observable\"]),\n    patternMatch: define(\"symbol/pattern-match\", [\n      \"esnext.symbol.pattern-match\",\n    ]),\n    replace: define(\"symbol/replace\", [\n      \"es.symbol.replace\",\n      \"es.string.replace\",\n    ]),\n    search: define(\"symbol/search\", [\"es.symbol.search\", \"es.string.search\"]),\n    species: define(\"symbol/species\", [\n      \"es.symbol.species\",\n      \"es.array.species\",\n    ]),\n    split: define(\"symbol/split\", [\"es.symbol.split\", \"es.string.split\"]),\n    toPrimitive: define(\"symbol/to-primitive\", [\n      \"es.symbol.to-primitive\",\n      \"es.date.to-primitive\",\n    ]),\n    toStringTag: define(\"symbol/to-string-tag\", [\n      \"es.symbol.to-string-tag\",\n      \"es.object.to-string\",\n      \"es.math.to-string-tag\",\n      \"es.json.to-string-tag\",\n    ]),\n    unscopables: define(\"symbol/unscopables\", [\"es.symbol.unscopables\"]),\n  },\n\n  URL: {\n    canParse: define(\"url/can-parse\", [\"web.url.can-parse\", \"web.url\"]),\n  },\n\n  WeakMap: {\n    from: define(null, [\"esnext.weak-map.from\", ...WeakMapDependencies]),\n    of: define(null, [\"esnext.weak-map.of\", ...WeakMapDependencies]),\n  },\n\n  WeakSet: {\n    from: define(null, [\"esnext.weak-set.from\", ...WeakSetDependencies]),\n    of: define(null, [\"esnext.weak-set.of\", ...WeakSetDependencies]),\n  },\n\n  Int8Array: TypedArrayStaticMethods,\n  Uint8Array: {\n    fromBase64: define(null, [\n      \"esnext.uint8-array.from-base64\",\n      ...TypedArrayDependencies,\n    ]),\n    fromHex: define(null, [\n      \"esnext.uint8-array.from-hex\",\n      ...TypedArrayDependencies,\n    ]),\n    ...TypedArrayStaticMethods,\n  },\n  Uint8ClampedArray: TypedArrayStaticMethods,\n  Int16Array: TypedArrayStaticMethods,\n  Uint16Array: TypedArrayStaticMethods,\n  Int32Array: TypedArrayStaticMethods,\n  Uint32Array: TypedArrayStaticMethods,\n  Float32Array: TypedArrayStaticMethods,\n  Float64Array: TypedArrayStaticMethods,\n\n  WebAssembly: {\n    CompileError: define(null, ErrorDependencies),\n    LinkError: define(null, ErrorDependencies),\n    RuntimeError: define(null, ErrorDependencies),\n  },\n};\n\nexport const InstanceProperties = {\n  asIndexedPairs: define(\"instance/asIndexedPairs\", [\n    \"esnext.async-iterator.as-indexed-pairs\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.as-indexed-pairs\",\n    ...IteratorDependencies,\n  ]),\n  at: define(\"instance/at\", [\n    // TODO: We should introduce overloaded instance methods definition\n    // Before that is implemented, the `esnext.string.at` must be the first\n    // In pure mode, the provider resolves the descriptor as a \"pure\" `esnext.string.at`\n    // and treats the compat-data of `esnext.string.at` as the compat-data of\n    // pure import `instance/at`. The first polyfill here should have the lowest corejs\n    // supported versions.\n    \"esnext.string.at\",\n    \"es.string.at-alternative\",\n    \"es.array.at\",\n  ]),\n  anchor: define(null, [\"es.string.anchor\"]),\n  big: define(null, [\"es.string.big\"]),\n  bind: define(\"instance/bind\", [\"es.function.bind\"]),\n  blink: define(null, [\"es.string.blink\"]),\n  bold: define(null, [\"es.string.bold\"]),\n  codePointAt: define(\"instance/code-point-at\", [\"es.string.code-point-at\"]),\n  codePoints: define(\"instance/code-points\", [\"esnext.string.code-points\"]),\n  concat: define(\"instance/concat\", [\"es.array.concat\"], undefined, [\"String\"]),\n  copyWithin: define(\"instance/copy-within\", [\"es.array.copy-within\"]),\n  demethodize: define(\"instance/demethodize\", [\"esnext.function.demethodize\"]),\n  description: define(null, [\"es.symbol\", \"es.symbol.description\"]),\n  dotAll: define(null, [\"es.regexp.dot-all\"]),\n  drop: define(null, [\n    \"esnext.async-iterator.drop\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.drop\",\n    ...IteratorDependencies,\n  ]),\n  emplace: define(\"instance/emplace\", [\n    \"esnext.map.emplace\",\n    \"esnext.weak-map.emplace\",\n  ]),\n  endsWith: define(\"instance/ends-with\", [\"es.string.ends-with\"]),\n  entries: define(\"instance/entries\", ArrayNatureIteratorsWithTag),\n  every: define(\"instance/every\", [\n    \"es.array.every\",\n    \"esnext.async-iterator.every\",\n    // TODO: add async iterator dependencies when we support sub-dependencies\n    // esnext.async-iterator.every depends on es.promise\n    // but we don't want to pull es.promise when esnext.async-iterator is disabled\n    //\n    // ...AsyncIteratorDependencies\n    \"esnext.iterator.every\",\n    ...IteratorDependencies,\n  ]),\n  exec: define(null, [\"es.regexp.exec\"]),\n  fill: define(\"instance/fill\", [\"es.array.fill\"]),\n  filter: define(\"instance/filter\", [\n    \"es.array.filter\",\n    \"esnext.async-iterator.filter\",\n    \"esnext.iterator.filter\",\n    ...IteratorDependencies,\n  ]),\n  filterReject: define(\"instance/filterReject\", [\"esnext.array.filter-reject\"]),\n  finally: define(null, [\"es.promise.finally\", ...PromiseDependencies]),\n  find: define(\"instance/find\", [\n    \"es.array.find\",\n    \"esnext.async-iterator.find\",\n    \"esnext.iterator.find\",\n    ...IteratorDependencies,\n  ]),\n  findIndex: define(\"instance/find-index\", [\"es.array.find-index\"]),\n  findLast: define(\"instance/find-last\", [\"es.array.find-last\"]),\n  findLastIndex: define(\"instance/find-last-index\", [\n    \"es.array.find-last-index\",\n  ]),\n  fixed: define(null, [\"es.string.fixed\"]),\n  flags: define(\"instance/flags\", [\"es.regexp.flags\"]),\n  flatMap: define(\"instance/flat-map\", [\n    \"es.array.flat-map\",\n    \"es.array.unscopables.flat-map\",\n    \"esnext.async-iterator.flat-map\",\n    \"esnext.iterator.flat-map\",\n    ...IteratorDependencies,\n  ]),\n  flat: define(\"instance/flat\", [\"es.array.flat\", \"es.array.unscopables.flat\"]),\n  getFloat16: define(null, [\n    \"esnext.data-view.get-float16\",\n    ...DataViewDependencies,\n  ]),\n  getUint8Clamped: define(null, [\n    \"esnext.data-view.get-uint8-clamped\",\n    ...DataViewDependencies,\n  ]),\n  getYear: define(null, [\"es.date.get-year\"]),\n  group: define(\"instance/group\", [\"esnext.array.group\"]),\n  groupBy: define(\"instance/group-by\", [\"esnext.array.group-by\"]),\n  groupByToMap: define(\"instance/group-by-to-map\", [\n    \"esnext.array.group-by-to-map\",\n    \"es.map\",\n    \"es.object.to-string\",\n  ]),\n  groupToMap: define(\"instance/group-to-map\", [\n    \"esnext.array.group-to-map\",\n    \"es.map\",\n    \"es.object.to-string\",\n  ]),\n  fontcolor: define(null, [\"es.string.fontcolor\"]),\n  fontsize: define(null, [\"es.string.fontsize\"]),\n  forEach: define(\"instance/for-each\", [\n    \"es.array.for-each\",\n    \"esnext.async-iterator.for-each\",\n    \"esnext.iterator.for-each\",\n    ...IteratorDependencies,\n    \"web.dom-collections.for-each\",\n  ]),\n  includes: define(\"instance/includes\", [\n    \"es.array.includes\",\n    \"es.string.includes\",\n  ]),\n  indexed: define(null, [\n    \"esnext.async-iterator.indexed\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.indexed\",\n    ...IteratorDependencies,\n  ]),\n  indexOf: define(\"instance/index-of\", [\"es.array.index-of\"]),\n  isWellFormed: define(\"instance/is-well-formed\", [\"es.string.is-well-formed\"]),\n  italic: define(null, [\"es.string.italics\"]),\n  join: define(null, [\"es.array.join\"]),\n  keys: define(\"instance/keys\", ArrayNatureIteratorsWithTag),\n  lastIndex: define(null, [\"esnext.array.last-index\"]),\n  lastIndexOf: define(\"instance/last-index-of\", [\"es.array.last-index-of\"]),\n  lastItem: define(null, [\"esnext.array.last-item\"]),\n  link: define(null, [\"es.string.link\"]),\n  map: define(\"instance/map\", [\n    \"es.array.map\",\n    \"esnext.async-iterator.map\",\n    \"esnext.iterator.map\",\n  ]),\n  match: define(null, [\"es.string.match\", \"es.regexp.exec\"]),\n  matchAll: define(\"instance/match-all\", [\n    \"es.string.match-all\",\n    \"es.regexp.exec\",\n  ]),\n  name: define(null, [\"es.function.name\"]),\n  padEnd: define(\"instance/pad-end\", [\"es.string.pad-end\"]),\n  padStart: define(\"instance/pad-start\", [\"es.string.pad-start\"]),\n  push: define(\"instance/push\", [\"es.array.push\"]),\n  reduce: define(\"instance/reduce\", [\n    \"es.array.reduce\",\n    \"esnext.async-iterator.reduce\",\n    \"esnext.iterator.reduce\",\n    ...IteratorDependencies,\n  ]),\n  reduceRight: define(\"instance/reduce-right\", [\"es.array.reduce-right\"]),\n  repeat: define(\"instance/repeat\", [\"es.string.repeat\"]),\n  replace: define(null, [\"es.string.replace\", \"es.regexp.exec\"]),\n  replaceAll: define(\"instance/replace-all\", [\n    \"es.string.replace-all\",\n    \"es.string.replace\",\n    \"es.regexp.exec\",\n  ]),\n  reverse: define(\"instance/reverse\", [\"es.array.reverse\"]),\n  search: define(null, [\"es.string.search\", \"es.regexp.exec\"]),\n  setFloat16: define(null, [\n    \"esnext.data-view.set-float16\",\n    ...DataViewDependencies,\n  ]),\n  setUint8Clamped: define(null, [\n    \"esnext.data-view.set-uint8-clamped\",\n    ...DataViewDependencies,\n  ]),\n  setYear: define(null, [\"es.date.set-year\"]),\n  slice: define(\"instance/slice\", [\"es.array.slice\"]),\n  small: define(null, [\"es.string.small\"]),\n  some: define(\"instance/some\", [\n    \"es.array.some\",\n    \"esnext.async-iterator.some\",\n    \"esnext.iterator.some\",\n    ...IteratorDependencies,\n  ]),\n  sort: define(\"instance/sort\", [\"es.array.sort\"]),\n  splice: define(\"instance/splice\", [\"es.array.splice\"]),\n  split: define(null, [\"es.string.split\", \"es.regexp.exec\"]),\n  startsWith: define(\"instance/starts-with\", [\"es.string.starts-with\"]),\n  sticky: define(null, [\"es.regexp.sticky\"]),\n  strike: define(null, [\"es.string.strike\"]),\n  sub: define(null, [\"es.string.sub\"]),\n  substr: define(null, [\"es.string.substr\"]),\n  sup: define(null, [\"es.string.sup\"]),\n  take: define(null, [\n    \"esnext.async-iterator.take\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.take\",\n    ...IteratorDependencies,\n  ]),\n  test: define(null, [\"es.regexp.test\", \"es.regexp.exec\"]),\n  toArray: define(null, [\n    \"esnext.async-iterator.to-array\",\n    ...AsyncIteratorDependencies,\n    \"esnext.iterator.to-array\",\n    ...IteratorDependencies,\n  ]),\n  toAsync: define(null, [\n    \"esnext.iterator.to-async\",\n    ...IteratorDependencies,\n    ...AsyncIteratorDependencies,\n    ...AsyncIteratorProblemMethods,\n  ]),\n  toExponential: define(null, [\"es.number.to-exponential\"]),\n  toFixed: define(null, [\"es.number.to-fixed\"]),\n  toGMTString: define(null, [\"es.date.to-gmt-string\"]),\n  toISOString: define(null, [\"es.date.to-iso-string\"]),\n  toJSON: define(null, [\"es.date.to-json\", \"web.url.to-json\"]),\n  toPrecision: define(null, [\"es.number.to-precision\"]),\n  toReversed: define(\"instance/to-reversed\", [\"es.array.to-reversed\"]),\n  toSorted: define(\"instance/to-sorted\", [\n    \"es.array.to-sorted\",\n    \"es.array.sort\",\n  ]),\n  toSpliced: define(\"instance/to-spliced\", [\"es.array.to-spliced\"]),\n  toString: define(null, [\n    \"es.object.to-string\",\n    \"es.error.to-string\",\n    \"es.date.to-string\",\n    \"es.regexp.to-string\",\n  ]),\n  toWellFormed: define(\"instance/to-well-formed\", [\"es.string.to-well-formed\"]),\n  trim: define(\"instance/trim\", [\"es.string.trim\"]),\n  trimEnd: define(\"instance/trim-end\", [\"es.string.trim-end\"]),\n  trimLeft: define(\"instance/trim-left\", [\"es.string.trim-start\"]),\n  trimRight: define(\"instance/trim-right\", [\"es.string.trim-end\"]),\n  trimStart: define(\"instance/trim-start\", [\"es.string.trim-start\"]),\n  uniqueBy: define(\"instance/unique-by\", [\"esnext.array.unique-by\", \"es.map\"]),\n  unshift: define(\"instance/unshift\", [\"es.array.unshift\"]),\n  unThis: define(\"instance/un-this\", [\"esnext.function.un-this\"]),\n  values: define(\"instance/values\", ArrayNatureIteratorsWithTag),\n  with: define(\"instance/with\", [\"es.array.with\"]),\n  __defineGetter__: define(null, [\"es.object.define-getter\"]),\n  __defineSetter__: define(null, [\"es.object.define-setter\"]),\n  __lookupGetter__: define(null, [\"es.object.lookup-getter\"]),\n  __lookupSetter__: define(null, [\"es.object.lookup-setter\"]),\n  [\"__proto__\"]: define(null, [\"es.object.proto\"]),\n};\n", "import type { CoreJSPolyfillDescriptor } from \"./built-in-definitions\";\nimport { types as t, type NodePath } from \"@babel/core\";\n\nexport default function canSkipPolyfill(\n  desc: CoreJSPolyfillDescriptor,\n  path: NodePath,\n) {\n  const { node, parent } = path;\n  switch (desc.name) {\n    case \"es.string.split\": {\n      if (!t.isCallExpression(parent, { callee: node })) return false;\n      if (parent.arguments.length < 1) return true;\n      const splitter = parent.arguments[0];\n      return t.isStringLiteral(splitter) || t.isTemplateLiteral(splitter);\n    }\n  }\n}\n", "import { types as t } from \"@babel/core\";\nimport corejsEntries from \"../core-js-compat/entries.js\";\n\nexport const BABEL_RUNTIME = \"@babel/runtime-corejs3\";\n\nexport function callMethod(path: any, id: t.Identifier) {\n  const { object } = path.node;\n\n  let context1, context2;\n  if (t.isIdentifier(object)) {\n    context1 = object;\n    context2 = t.cloneNode(object);\n  } else {\n    context1 = path.scope.generateDeclaredUidIdentifier(\"context\");\n    context2 = t.assignmentExpression(\"=\", t.cloneNode(context1), object);\n  }\n\n  path.replaceWith(\n    t.memberExpression(t.callExpression(id, [context2]), t.identifier(\"call\")),\n  );\n\n  path.parentPath.unshiftContainer(\"arguments\", context1);\n}\n\nexport function isCoreJSSource(source: string) {\n  if (typeof source === \"string\") {\n    source = source\n      .replace(/\\\\/g, \"/\")\n      .replace(/(\\/(index)?)?(\\.js)?$/i, \"\")\n      .toLowerCase();\n  }\n\n  return (\n    Object.prototype.hasOwnProperty.call(corejsEntries, source) &&\n    corejsEntries[source]\n  );\n}\n\nexport function coreJSModule(name: string) {\n  return `core-js/modules/${name}.js`;\n}\n\nexport function coreJSPureHelper(\n  name: string,\n  useBabelRuntime: boolean,\n  ext: string,\n) {\n  return useBabelRuntime\n    ? `${BABEL_RUNTIME}/core-js/${name}${ext}`\n    : `core-js-pure/features/${name}.js`;\n}\n", "import corejs3Polyfills from \"../core-js-compat/data.js\";\nimport corejs3ShippedProposalsList from \"./shipped-proposals\";\nimport getModulesListForTargetVersion from \"../core-js-compat/get-modules-list-for-target-version.js\";\nimport {\n  BuiltIns,\n  CommonIterators,\n  PromiseDependencies,\n  PromiseDependenciesWithIterators,\n  StaticProperties,\n  InstanceProperties,\n  DecoratorMetadataDependencies,\n  type CoreJSPolyfillDescriptor,\n} from \"./built-in-definitions\";\nimport canSkipPolyfill from \"./usage-filters\";\n\nimport type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\nimport {\n  callMethod,\n  coreJSModule,\n  isCoreJSSource,\n  coreJSPureHelper,\n  BABEL_RUNTIME,\n} from \"./utils\";\n\nimport defineProvider from \"@babel/helper-define-polyfill-provider\";\n\nconst presetEnvCompat = \"#__secret_key__@babel/preset-env__compatibility\";\nconst runtimeCompat = \"#__secret_key__@babel/runtime__compatibility\";\n\ntype Options = {\n  version?: number | string;\n  proposals?: boolean;\n  shippedProposals?: boolean;\n  [presetEnvCompat]?: { noRuntimeName: boolean };\n  [runtimeCompat]: {\n    useBabelRuntime: boolean;\n    babelRuntimePath: string;\n    ext: string;\n  };\n};\n\nconst uniqueObjects = [\n  \"array\",\n  \"string\",\n\n  \"iterator\",\n  \"async-iterator\",\n  \"dom-collections\",\n].map(v => new RegExp(`[a-z]*\\\\.${v}\\\\..*`));\n\nconst esnextFallback = (\n  name: string,\n  cb: (name: string) => boolean,\n): boolean => {\n  if (cb(name)) return true;\n  if (!name.startsWith(\"es.\")) return false;\n  const fallback = `esnext.${name.slice(3)}`;\n  if (!corejs3Polyfills[fallback]) return false;\n  return cb(fallback);\n};\n\nexport default defineProvider<Options>(function (\n  { getUtils, method, shouldInjectPolyfill, createMetaResolver, debug, babel },\n  {\n    version = 3,\n    proposals,\n    shippedProposals,\n    [presetEnvCompat]: { noRuntimeName = false } = {},\n    [runtimeCompat]: { useBabelRuntime = false, ext = \".js\" } = {},\n  },\n) {\n  const isWebpack = babel.caller(caller => caller?.name === \"babel-loader\");\n\n  const resolve = createMetaResolver({\n    global: BuiltIns,\n    static: StaticProperties,\n    instance: InstanceProperties,\n  });\n\n  const available = new Set(getModulesListForTargetVersion(version));\n\n  function getCoreJSPureBase(useProposalBase) {\n    return useBabelRuntime\n      ? useProposalBase\n        ? `${BABEL_RUNTIME}/core-js`\n        : `${BABEL_RUNTIME}/core-js-stable`\n      : useProposalBase\n      ? \"core-js-pure/features\"\n      : \"core-js-pure/stable\";\n  }\n\n  function maybeInjectGlobalImpl(name: string, utils) {\n    if (shouldInjectPolyfill(name)) {\n      debug(name);\n      utils.injectGlobalImport(coreJSModule(name), name);\n      return true;\n    }\n    return false;\n  }\n\n  function maybeInjectGlobal(names: string[], utils, fallback = true) {\n    for (const name of names) {\n      if (fallback) {\n        esnextFallback(name, name => maybeInjectGlobalImpl(name, utils));\n      } else {\n        maybeInjectGlobalImpl(name, utils);\n      }\n    }\n  }\n\n  function maybeInjectPure(\n    desc: CoreJSPolyfillDescriptor,\n    hint,\n    utils,\n    object?,\n  ) {\n    if (\n      desc.pure &&\n      !(object && desc.exclude && desc.exclude.includes(object)) &&\n      esnextFallback(desc.name, shouldInjectPolyfill)\n    ) {\n      const { name } = desc;\n      let useProposalBase = false;\n      if (proposals || (shippedProposals && name.startsWith(\"esnext.\"))) {\n        useProposalBase = true;\n      } else if (name.startsWith(\"es.\") && !available.has(name)) {\n        useProposalBase = true;\n      }\n      const coreJSPureBase = getCoreJSPureBase(useProposalBase);\n      return utils.injectDefaultImport(\n        `${coreJSPureBase}/${desc.pure}${ext}`,\n        hint,\n      );\n    }\n  }\n\n  function isFeatureStable(name) {\n    if (name.startsWith(\"esnext.\")) {\n      const esName = `es.${name.slice(7)}`;\n      // If its imaginative esName is not in latest compat data, it means\n      // the proposal is not stage 4\n      return esName in corejs3Polyfills;\n    }\n    return true;\n  }\n\n  return {\n    name: \"corejs3\",\n\n    runtimeName: noRuntimeName ? null : BABEL_RUNTIME,\n\n    polyfills: corejs3Polyfills,\n\n    filterPolyfills(name) {\n      if (!available.has(name)) return false;\n      if (proposals || method === \"entry-global\") return true;\n      if (shippedProposals && corejs3ShippedProposalsList.has(name)) {\n        return true;\n      }\n      return isFeatureStable(name);\n    },\n\n    entryGlobal(meta, utils, path) {\n      if (meta.kind !== \"import\") return;\n\n      const modules = isCoreJSSource(meta.source);\n      if (!modules) return;\n\n      if (\n        modules.length === 1 &&\n        meta.source === coreJSModule(modules[0]) &&\n        shouldInjectPolyfill(modules[0])\n      ) {\n        // Avoid infinite loop: do not replace imports with a new copy of\n        // themselves.\n        debug(null);\n        return;\n      }\n\n      const modulesSet = new Set(modules);\n      const filteredModules = modules.filter(module => {\n        if (!module.startsWith(\"esnext.\")) return true;\n        const stable = module.replace(\"esnext.\", \"es.\");\n        if (modulesSet.has(stable) && shouldInjectPolyfill(stable)) {\n          return false;\n        }\n        return true;\n      });\n\n      maybeInjectGlobal(filteredModules, utils, false);\n      path.remove();\n    },\n\n    usageGlobal(meta, utils, path) {\n      const resolved = resolve(meta);\n      if (!resolved) return;\n\n      if (canSkipPolyfill(resolved.desc, path)) return;\n\n      let deps = resolved.desc.global;\n\n      if (\n        resolved.kind !== \"global\" &&\n        \"object\" in meta &&\n        meta.object &&\n        meta.placement === \"prototype\"\n      ) {\n        const low = meta.object.toLowerCase();\n        deps = deps.filter(m =>\n          uniqueObjects.some(v => v.test(m)) ? m.includes(low) : true,\n        );\n      }\n\n      maybeInjectGlobal(deps, utils);\n    },\n\n    usagePure(meta, utils, path) {\n      if (meta.kind === \"in\") {\n        if (meta.key === \"Symbol.iterator\") {\n          path.replaceWith(\n            t.callExpression(\n              utils.injectDefaultImport(\n                coreJSPureHelper(\"is-iterable\", useBabelRuntime, ext),\n                \"isIterable\",\n              ),\n              [(path.node as t.BinaryExpression).right], // meta.kind === \"in\" narrows this\n            ),\n          );\n        }\n        return;\n      }\n\n      if (path.parentPath.isUnaryExpression({ operator: \"delete\" })) return;\n\n      if (meta.kind === \"property\") {\n        // We can't compile destructuring and updateExpression.\n        if (!path.isMemberExpression()) return;\n        if (!path.isReferenced()) return;\n        if (path.parentPath.isUpdateExpression()) return;\n        if (t.isSuper(path.node.object)) {\n          return;\n        }\n\n        if (meta.key === \"Symbol.iterator\") {\n          if (!shouldInjectPolyfill(\"es.symbol.iterator\")) return;\n\n          const { parent, node } = path;\n          if (t.isCallExpression(parent, { callee: node })) {\n            if (parent.arguments.length === 0) {\n              path.parentPath.replaceWith(\n                t.callExpression(\n                  utils.injectDefaultImport(\n                    coreJSPureHelper(\"get-iterator\", useBabelRuntime, ext),\n                    \"getIterator\",\n                  ),\n                  [node.object],\n                ),\n              );\n              path.skip();\n            } else {\n              callMethod(\n                path,\n                utils.injectDefaultImport(\n                  coreJSPureHelper(\"get-iterator-method\", useBabelRuntime, ext),\n                  \"getIteratorMethod\",\n                ),\n              );\n            }\n          } else {\n            path.replaceWith(\n              t.callExpression(\n                utils.injectDefaultImport(\n                  coreJSPureHelper(\"get-iterator-method\", useBabelRuntime, ext),\n                  \"getIteratorMethod\",\n                ),\n                [path.node.object],\n              ),\n            );\n          }\n\n          return;\n        }\n      }\n\n      let resolved = resolve(meta);\n      if (!resolved) return;\n\n      if (canSkipPolyfill(resolved.desc, path)) return;\n\n      if (\n        useBabelRuntime &&\n        resolved.desc.pure &&\n        resolved.desc.pure.slice(-6) === \"/index\"\n      ) {\n        // Remove /index, since it doesn't exist in @babel/runtime-corejs3s\n        resolved = {\n          ...resolved,\n          desc: {\n            ...resolved.desc,\n            pure: resolved.desc.pure.slice(0, -6),\n          },\n        };\n      }\n\n      if (resolved.kind === \"global\") {\n        const id = maybeInjectPure(resolved.desc, resolved.name, utils);\n        if (id) path.replaceWith(id);\n      } else if (resolved.kind === \"static\") {\n        const id = maybeInjectPure(\n          resolved.desc,\n          resolved.name,\n          utils,\n          // @ts-expect-error\n          meta.object,\n        );\n        if (id) path.replaceWith(id);\n      } else if (resolved.kind === \"instance\") {\n        const id = maybeInjectPure(\n          resolved.desc,\n          `${resolved.name}InstanceProperty`,\n          utils,\n          // @ts-expect-error\n          meta.object,\n        );\n        if (!id) return;\n\n        const { node } = path as NodePath<t.MemberExpression>;\n        if (t.isCallExpression(path.parent, { callee: node })) {\n          callMethod(path, id);\n        } else {\n          path.replaceWith(t.callExpression(id, [node.object]));\n        }\n      }\n    },\n\n    visitor: method === \"usage-global\" && {\n      // import(\"foo\")\n      CallExpression(path: NodePath<t.CallExpression>) {\n        if (path.get(\"callee\").isImport()) {\n          const utils = getUtils(path);\n\n          if (isWebpack) {\n            // Webpack uses Promise.all to handle dynamic import.\n            maybeInjectGlobal(PromiseDependenciesWithIterators, utils);\n          } else {\n            maybeInjectGlobal(PromiseDependencies, utils);\n          }\n        }\n      },\n\n      // (async function () { }).finally(...)\n      Function(path: NodePath<t.Function>) {\n        if (path.node.async) {\n          maybeInjectGlobal(PromiseDependencies, getUtils(path));\n        }\n      },\n\n      // for-of, [a, b] = c\n      \"ForOfStatement|ArrayPattern\"(\n        path: NodePath<t.ForOfStatement | t.ArrayPattern>,\n      ) {\n        maybeInjectGlobal(CommonIterators, getUtils(path));\n      },\n\n      // [...spread]\n      SpreadElement(path: NodePath<t.SpreadElement>) {\n        if (!path.parentPath.isObjectExpression()) {\n          maybeInjectGlobal(CommonIterators, getUtils(path));\n        }\n      },\n\n      // yield*\n      YieldExpression(path: NodePath<t.YieldExpression>) {\n        if (path.node.delegate) {\n          maybeInjectGlobal(CommonIterators, getUtils(path));\n        }\n      },\n\n      // Decorators metadata\n      Class(path: NodePath<t.Class>) {\n        const hasDecorators =\n          path.node.decorators?.length ||\n          path.node.body.body.some(\n            el => (el as t.ClassMethod).decorators?.length,\n          );\n        if (hasDecorators) {\n          maybeInjectGlobal(DecoratorMetadataDependencies, getUtils(path));\n        }\n      },\n    },\n  };\n});\n"], "names": ["Set", "polyfillsOrder", "Object", "keys", "corejs3Polyfills", "for<PERSON>ach", "name", "index", "define", "pure", "global", "exclude", "sort", "a", "b", "typed", "modules", "TypedArrayDependencies", "ArrayNatureIterators", "CommonIterators", "ArrayNatureIteratorsWithTag", "CommonIteratorsWithTag", "ErrorDependencies", "SuppressedErrorDependencies", "PromiseDependencies", "PromiseDependenciesWithIterators", "SymbolDependencies", "MapDependencies", "SetDependencies", "WeakMapDependencies", "WeakSetDependencies", "DOMExceptionDependencies", "URLSearchParamsDependencies", "AsyncIteratorDependencies", "AsyncIteratorProblemMethods", "IteratorDependencies", "DecoratorMetadataDependencies", "TypedArrayStaticMethods", "from", "fromAsync", "of", "DataViewDependencies", "BuiltIns", "AsyncDisposableStack", "AsyncIterator", "AggregateError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Date", "DOMException", "DisposableStack", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Iterator", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "Map", "Number", "Observable", "Promise", "RangeError", "ReferenceError", "Reflect", "RegExp", "SuppressedError", "Symbol", "SyntaxError", "TypeError", "URIError", "URL", "URLSearchParams", "WeakMap", "WeakSet", "atob", "btoa", "clearImmediate", "compositeKey", "compositeSymbol", "escape", "fetch", "globalThis", "parseFloat", "parseInt", "queueMicrotask", "self", "setImmediate", "setInterval", "setTimeout", "structuredClone", "unescape", "StaticProperties", "Array", "isArray", "isTemplateObject", "<PERSON><PERSON><PERSON><PERSON>", "BigInt", "range", "now", "Function", "isCallable", "isConstructor", "JSON", "isRawJSON", "parse", "rawJSON", "stringify", "Math", "DEG_PER_RAD", "RAD_PER_DEG", "acosh", "asinh", "atanh", "cbrt", "clamp", "clz32", "cosh", "degrees", "expm1", "fround", "f16round", "fscale", "hypot", "iaddh", "imul", "imulh", "<PERSON><PERSON><PERSON>", "log10", "log1p", "log2", "radians", "scale", "seededPRNG", "sign", "signbit", "sinh", "tanh", "trunc", "umulh", "groupBy", "keyBy", "EPSILON", "MAX_SAFE_INTEGER", "MIN_SAFE_INTEGER", "fromString", "isFinite", "isInteger", "isNaN", "isSafeInteger", "assign", "create", "defineProperties", "defineProperty", "entries", "freeze", "fromEntries", "getOwnPropertyDescriptor", "getOwnPropertyDescriptors", "getOwnPropertyNames", "getOwnPropertySymbols", "getPrototypeOf", "hasOwn", "is", "isExtensible", "isFrozen", "isSealed", "preventExtensions", "seal", "setPrototypeOf", "values", "all", "allSettled", "any", "race", "try", "withResolvers", "apply", "construct", "defineMetadata", "deleteMetadata", "deleteProperty", "get", "getMetadata", "getMetadataKeys", "getOwnMetadata", "getOwnMetadataKeys", "has", "hasMetadata", "hasOwnMetadata", "metadata", "ownKeys", "set", "String", "cooked", "dedent", "fromCodePoint", "raw", "asyncDispose", "asyncIterator", "dispose", "for", "hasInstance", "isConcatSpreadable", "isRegistered", "isRegisteredSymbol", "isWellKnown", "isWellKnownSymbol", "iterator", "keyFor", "match", "matcher", "matchAll", "metadataKey", "observable", "patternMatch", "replace", "search", "species", "split", "toPrimitive", "toStringTag", "unscopables", "canParse", "fromBase64", "fromHex", "WebAssembly", "CompileError", "LinkError", "RuntimeError", "InstanceProperties", "asIndexedPairs", "at", "anchor", "big", "bind", "blink", "bold", "codePointAt", "codePoints", "concat", "undefined", "copyWithin", "demethodize", "description", "dotAll", "drop", "emplace", "endsWith", "every", "exec", "fill", "filter", "filterReject", "finally", "find", "findIndex", "findLast", "findLastIndex", "fixed", "flags", "flatMap", "flat", "getFloat16", "getUint8Clamped", "getYear", "group", "groupByToMap", "groupToMap", "fontcolor", "fontsize", "includes", "indexed", "indexOf", "isWellFormed", "italic", "join", "lastIndex", "lastIndexOf", "lastItem", "link", "map", "padEnd", "padStart", "push", "reduce", "reduceRight", "repeat", "replaceAll", "reverse", "setFloat16", "setUint8Clamped", "setYear", "slice", "small", "some", "splice", "startsWith", "sticky", "strike", "sub", "substr", "sup", "take", "test", "toArray", "to<PERSON><PERSON>", "toExponential", "toFixed", "toGMTString", "toISOString", "toJSON", "toPrecision", "toReversed", "toSorted", "toSpliced", "toString", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "trim", "trimEnd", "trimLeft", "trimRight", "trimStart", "uniqueBy", "unshift", "unThis", "with", "__defineGetter__", "__defineSetter__", "__lookupGetter__", "__lookupSetter__", "types", "t", "_babel", "default", "canSkipPolyfill", "desc", "path", "node", "parent", "isCallExpression", "callee", "arguments", "length", "splitter", "isStringLiteral", "isTemplateLiteral", "BABEL_RUNTIME", "callMethod", "id", "object", "context1", "context2", "isIdentifier", "cloneNode", "scope", "generateDeclaredUidIdentifier", "assignmentExpression", "replaceWith", "memberExpression", "callExpression", "identifier", "parentPath", "unshiftContainer", "isCoreJSSource", "source", "toLowerCase", "prototype", "hasOwnProperty", "call", "corejsEntries", "coreJSModule", "coreJSPureHelper", "useBabelRuntime", "ext", "presetEnvCompat", "runtimeCompat", "uniqueObjects", "v", "esnextFallback", "cb", "fallback", "define<PERSON>rovider", "getUtils", "method", "shouldInjectPolyfill", "createMetaResolver", "debug", "babel", "version", "proposals", "shippedProposals", "noRuntimeName", "isWebpack", "caller", "resolve", "static", "instance", "available", "getModulesListForTargetVersion", "getCoreJSPureBase", "useProposalBase", "maybeInjectGlobalImpl", "utils", "injectGlobalImport", "maybeInjectGlobal", "names", "maybeInjectPure", "hint", "coreJSPureBase", "injectDefaultImport", "isFeatureStable", "esName", "runtimeName", "polyfills", "filterPolyfills", "corejs3ShippedProposalsList", "entryGlobal", "meta", "kind", "modulesSet", "filteredModules", "module", "stable", "remove", "usageGlobal", "resolved", "deps", "placement", "low", "m", "usagePure", "key", "right", "isUnaryExpression", "operator", "isMemberExpression", "isReferenced", "isUpdateExpression", "is<PERSON><PERSON><PERSON>", "skip", "visitor", "CallExpression", "isImport", "async", "ForOfStatement|ArrayPattern", "SpreadElement", "isObjectExpression", "YieldExpression", "delegate", "Class", "_path$node$decorators", "hasDecorators", "decorators", "body", "el", "_decorators"], "mappings": ";;;;;;AAAA;;AAEA,kCAAe,IAAIA,GAAG,CAAS,CAC7B,yBAAyB,EACzB,oBAAoB,EACpB,2BAA2B,EAC3B,8BAA8B,EAC9B,8BAA8B,EAC9B,8CAA8C,EAC9C,6BAA6B,EAC7B,sBAAsB,EACtB,uBAAuB,EACvB,wBAAwB,EACxB,sBAAsB,EACtB,0BAA0B,EAC1B,0BAA0B,EAC1B,sBAAsB,EACtB,qBAAqB,EACrB,wBAAwB,EACxB,sBAAsB,EACtB,sBAAsB,EACtB,0BAA0B,EAC1B,yBAAyB,EACzB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,4BAA4B,EAC5B,gCAAgC,EAChC,4BAA4B,EAC5B,8BAA8B,EAC9B,oCAAoC,EACpC,qBAAqB,EACrB,6BAA6B,EAC7B,uBAAuB,CACxB,CAAC;;ACtBF,MAAMC,cAAc,GAAG,EAAE;AACzBC,MAAM,CAACC,IAAI,CAACC,gBAAgB,CAAC,CAACC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;EACrDN,cAAc,CAACK,IAAI,CAAC,GAAGC,KAAK;AAC9B,CAAC,CAAC;AAEF,MAAMC,MAAM,GAAGA,CACbC,IAAI,EACJC,MAAM,EACNJ,IAAI,GAAGI,MAAM,CAAC,CAAC,CAAC,EAChBC,OAAQ,KACqB;EAC7B,OAAO;IACLL,IAAI;IACJG,IAAI;IACJC,MAAM,EAAEA,MAAM,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKb,cAAc,CAACY,CAAC,CAAC,GAAGZ,cAAc,CAACa,CAAC,CAAC,CAAC;IACpEH;GACD;AACH,CAAC;AAED,MAAMI,KAAK,GAAGA,CAAC,GAAGC,OAAO,KACvBR,MAAM,CAAC,IAAI,EAAE,CAAC,GAAGQ,OAAO,EAAE,GAAGC,sBAAsB,CAAC,CAAC;AAEvD,MAAMC,oBAAoB,GAAG,CAC3B,mBAAmB,EACnB,8BAA8B,CAC/B;AAEM,MAAMC,eAAe,GAAG,CAAC,oBAAoB,EAAE,GAAGD,oBAAoB,CAAC;AAE9E,MAAME,2BAA2B,GAAG,CAClC,qBAAqB,EACrB,GAAGF,oBAAoB,CACxB;AAED,MAAMG,sBAAsB,GAAG,CAAC,qBAAqB,EAAE,GAAGF,eAAe,CAAC;AAE1E,MAAMG,iBAAiB,GAAG,CAAC,gBAAgB,EAAE,oBAAoB,CAAC;AAElE,MAAMC,2BAA2B,GAAG,CAClC,qCAAqC,EACrC,GAAGD,iBAAiB,CACrB;AAED,MAAML,sBAAsB,GAAG,CAC7B,mBAAmB,EACnB,4BAA4B,EAC5B,sBAAsB,EACtB,qBAAqB,EACrB,uBAAuB,EACvB,qBAAqB,EACrB,2BAA2B,EAC3B,0BAA0B,EAC1B,gCAAgC,EAChC,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,qBAAqB,EACrB,8BAA8B,EAC9B,oBAAoB,EACpB,uBAAuB,EACvB,6BAA6B,EAC7B,wBAAwB,EACxB,oBAAoB,EACpB,sBAAsB,EACtB,qBAAqB,EACrB,qBAAqB,EACrB,yBAAyB,EACzB,iCAAiC,EACjC,4BAA4B,EAC5B,0BAA0B,EAC1B,0BAA0B,EAC1B,qBAAqB,EACrB,qBAAqB,EACrB,mBAAmB,EACnB,uBAAuB,EACvB,8BAA8B,EAC9B,8BAA8B,EAC9B,8CAA8C,EAC9C,kCAAkC,EAClC,6BAA6B,EAC7B,+BAA+B,EAC/B,8BAA8B,CAC/B;AAEM,MAAMO,mBAAmB,GAAG,CAAC,YAAY,EAAE,qBAAqB,CAAC;AAEjE,MAAMC,gCAAgC,GAAG,CAC9C,GAAGD,mBAAmB,EACtB,GAAGL,eAAe,CACnB;AAED,MAAMO,kBAAkB,GAAG,CACzB,WAAW,EACX,uBAAuB,EACvB,qBAAqB,CACtB;AAED,MAAMC,eAAe,GAAG,CACtB,QAAQ,EACR,uBAAuB,EACvB,oBAAoB,EACpB,kBAAkB,EAClB,mBAAmB,EACnB,iBAAiB,EACjB,qBAAqB,EACrB,qBAAqB,EACrB,mBAAmB,EACnB,qBAAqB,EACrB,uBAAuB,EACvB,kBAAkB,EAClB,mBAAmB,EACnB,iBAAiB,EACjB,mBAAmB,EACnB,GAAGN,sBAAsB,CAC1B;AAED,MAAMO,eAAe,GAAG,CACtB,QAAQ,EACR,oBAAoB,EACpB,uBAAuB,EACvB,uBAAuB,EACvB,0BAA0B,EAC1B,kBAAkB,EAClB,mBAAmB,EACnB,iBAAiB,EACjB,yBAAyB,EACzB,4BAA4B,EAC5B,6BAA6B,EAC7B,gCAAgC,EAChC,yBAAyB,EACzB,4BAA4B,EAC5B,2BAA2B,EAC3B,8BAA8B,EAC9B,iBAAiB,EACjB,gBAAgB,EAChB,mBAAmB,EACnB,iBAAiB,EACjB,iCAAiC,EACjC,oCAAoC,EACpC,kBAAkB,EAClB,qBAAqB,EACrB,GAAGP,sBAAsB,CAC1B;AAED,MAAMQ,mBAAmB,GAAG,CAC1B,aAAa,EACb,4BAA4B,EAC5B,yBAAyB,EACzB,GAAGR,sBAAsB,CAC1B;AAED,MAAMS,mBAAmB,GAAG,CAC1B,aAAa,EACb,yBAAyB,EACzB,4BAA4B,EAC5B,GAAGT,sBAAsB,CAC1B;AAED,MAAMU,wBAAwB,GAAG,CAC/B,+BAA+B,EAC/B,yBAAyB,EACzB,iCAAiC,EACjC,oBAAoB,CACrB;AAED,MAAMC,2BAA2B,GAAG,CAClC,uBAAuB,EACvB,8BAA8B,EAC9B,2BAA2B,EAC3B,4BAA4B,EAC5B,GAAGX,sBAAsB,CAC1B;AAED,MAAMY,yBAAyB,GAAG,CAChC,mCAAmC,EACnC,GAAGT,mBAAmB,CACvB;AAED,MAAMU,2BAA2B,GAAG,CAClC,6BAA6B,EAC7B,8BAA8B,EAC9B,4BAA4B,EAC5B,gCAAgC,EAChC,gCAAgC,EAChC,2BAA2B,EAC3B,8BAA8B,EAC9B,4BAA4B,CAC7B;AAED,MAAMC,oBAAoB,GAAG,CAC3B,6BAA6B,EAC7B,qBAAqB,CACtB;AAEM,MAAMC,6BAA6B,GAAG,CAC3C,wBAAwB,EACxB,0BAA0B,CAC3B;AAED,MAAMC,uBAAuB,GAAG;EAC9BC,IAAI,EAAE9B,MAAM,CAAC,IAAI,EAAE,CAAC,qBAAqB,EAAE,GAAGS,sBAAsB,CAAC,CAAC;EACtEsB,SAAS,EAAE/B,MAAM,CAAC,IAAI,EAAE,CACtB,+BAA+B,EAC/B,GAAGiB,gCAAgC,EACnC,GAAGR,sBAAsB,CAC1B,CAAC;EACFuB,EAAE,EAAEhC,MAAM,CAAC,IAAI,EAAE,CAAC,mBAAmB,EAAE,GAAGS,sBAAsB,CAAC;AACnE,CAAC;AAED,MAAMwB,oBAAoB,GAAG,CAC3B,cAAc,EACd,uBAAuB,EACvB,qBAAqB,CACtB;AAEM,MAAMC,QAA6C,GAAG;EAC3DC,oBAAoB,EAAEnC,MAAM,CAAC,8BAA8B,EAAE,CAC3D,2CAA2C,EAC3C,qBAAqB,EACrB,qCAAqC,EACrC,yBAAyB,EACzB,GAAGgB,mBAAmB,EACtB,GAAGD,2BAA2B,CAC/B,CAAC;EACFqB,aAAa,EAAEpC,MAAM,CAAC,sBAAsB,EAAEyB,yBAAyB,CAAC;EACxEY,cAAc,EAAErC,MAAM,CAAC,iBAAiB,EAAE,CACxC,oBAAoB,EACpB,GAAGc,iBAAiB,EACpB,GAAGD,sBAAsB,EACzB,0BAA0B,CAC3B,CAAC;EACFyB,WAAW,EAAEtC,MAAM,CAAC,IAAI,EAAE,CACxB,6BAA6B,EAC7B,uBAAuB,EACvB,qBAAqB,CACtB,CAAC;EACFuC,QAAQ,EAAEvC,MAAM,CAAC,IAAI,EAAEiC,oBAAoB,CAAC;EAC5CO,IAAI,EAAExC,MAAM,CAAC,IAAI,EAAE,CAAC,mBAAmB,CAAC,CAAC;EACzCyC,YAAY,EAAEzC,MAAM,CAAC,qBAAqB,EAAEuB,wBAAwB,CAAC;EACrEmB,eAAe,EAAE1C,MAAM,CAAC,wBAAwB,EAAE,CAChD,qCAAqC,EACrC,qBAAqB,EACrB,yBAAyB,EACzB,GAAGe,2BAA2B,CAC/B,CAAC;EACF4B,KAAK,EAAE3C,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;EACtC8B,SAAS,EAAE5C,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;EAC1C+B,YAAY,EAAEtC,KAAK,CAAC,8BAA8B,CAAC;EACnDuC,YAAY,EAAEvC,KAAK,CAAC,8BAA8B,CAAC;EACnDwC,SAAS,EAAExC,KAAK,CAAC,2BAA2B,CAAC;EAC7CyC,UAAU,EAAEzC,KAAK,CAAC,4BAA4B,CAAC;EAC/C0C,UAAU,EAAE1C,KAAK,CAAC,4BAA4B,CAAC;EAC/C2C,QAAQ,EAAElD,MAAM,CAAC,gBAAgB,EAAE2B,oBAAoB,CAAC;EACxDwB,UAAU,EAAE5C,KAAK,CACf,4BAA4B,EAC5B,8BAA8B,EAC9B,2BACF,CAAC;EACD6C,iBAAiB,EAAE7C,KAAK,CAAC,oCAAoC,CAAC;EAC9D8C,WAAW,EAAE9C,KAAK,CAAC,6BAA6B,CAAC;EACjD+C,WAAW,EAAE/C,KAAK,CAAC,6BAA6B,CAAC;EACjDgD,GAAG,EAAEvD,MAAM,CAAC,WAAW,EAAEmB,eAAe,CAAC;EACzCqC,MAAM,EAAExD,MAAM,CAAC,IAAI,EAAE,CAAC,uBAAuB,CAAC,CAAC;EAC/CyD,UAAU,EAAEzD,MAAM,CAAC,kBAAkB,EAAE,CACrC,mBAAmB,EACnB,0BAA0B,EAC1B,qBAAqB,EACrB,GAAGa,sBAAsB,CAC1B,CAAC;EACF6C,OAAO,EAAE1D,MAAM,CAAC,eAAe,EAAEgB,mBAAmB,CAAC;EACrD2C,UAAU,EAAE3D,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;EAC3C8C,cAAc,EAAE5D,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;EAC/C+C,OAAO,EAAE7D,MAAM,CAAC,IAAI,EAAE,CAAC,0BAA0B,EAAE,qBAAqB,CAAC,CAAC;EAC1E8D,MAAM,EAAE9D,MAAM,CAAC,IAAI,EAAE,CACnB,uBAAuB,EACvB,mBAAmB,EACnB,gBAAgB,EAChB,kBAAkB,EAClB,qBAAqB,CACtB,CAAC;EACFR,GAAG,EAAEQ,MAAM,CAAC,WAAW,EAAEoB,eAAe,CAAC;EACzC2C,eAAe,EAAE/D,MAAM,CAAC,kBAAkB,EAAEe,2BAA2B,CAAC;EACxEiD,MAAM,EAAEhE,MAAM,CAAC,cAAc,EAAEkB,kBAAkB,CAAC;EAClD+C,WAAW,EAAEjE,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;EAC5CoD,SAAS,EAAElE,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;EAC1CqD,QAAQ,EAAEnE,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;EACzCsD,GAAG,EAAEpE,MAAM,CAAC,WAAW,EAAE,CAAC,SAAS,EAAE,GAAGwB,2BAA2B,CAAC,CAAC;EACrE6C,eAAe,EAAErE,MAAM,CACrB,yBAAyB,EACzBwB,2BACF,CAAC;EACD8C,OAAO,EAAEtE,MAAM,CAAC,gBAAgB,EAAEqB,mBAAmB,CAAC;EACtDkD,OAAO,EAAEvE,MAAM,CAAC,gBAAgB,EAAEsB,mBAAmB,CAAC;EAEtDkD,IAAI,EAAExE,MAAM,CAAC,MAAM,EAAE,CAAC,UAAU,EAAE,GAAGuB,wBAAwB,CAAC,CAAC;EAC/DkD,IAAI,EAAEzE,MAAM,CAAC,MAAM,EAAE,CAAC,UAAU,EAAE,GAAGuB,wBAAwB,CAAC,CAAC;EAC/DmD,cAAc,EAAE1E,MAAM,CAAC,iBAAiB,EAAE,CAAC,eAAe,CAAC,CAAC;EAC5D2E,YAAY,EAAE3E,MAAM,CAAC,eAAe,EAAE,CAAC,sBAAsB,CAAC,CAAC;EAC/D4E,eAAe,EAAE5E,MAAM,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC;EACxE6E,MAAM,EAAE7E,MAAM,CAAC,QAAQ,EAAE,CAAC,WAAW,CAAC,CAAC;EACvC8E,KAAK,EAAE9E,MAAM,CAAC,IAAI,EAAEgB,mBAAmB,CAAC;EACxC+D,UAAU,EAAE/E,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACrDgF,UAAU,EAAEhF,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACrDiF,QAAQ,EAAEjF,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;EAC/CkF,cAAc,EAAElF,MAAM,CAAC,iBAAiB,EAAE,CAAC,qBAAqB,CAAC,CAAC;EAClEmF,IAAI,EAAEnF,MAAM,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,CAAC;EAClCoF,YAAY,EAAEpF,MAAM,CAAC,eAAe,EAAE,CAAC,eAAe,CAAC,CAAC;EACxDqF,WAAW,EAAErF,MAAM,CAAC,cAAc,EAAE,CAAC,YAAY,CAAC,CAAC;EACnDsF,UAAU,EAAEtF,MAAM,CAAC,aAAa,EAAE,CAAC,YAAY,CAAC,CAAC;EACjDuF,eAAe,EAAEvF,MAAM,CAAC,kBAAkB,EAAE,CAC1C,sBAAsB,EACtB,GAAGuB,wBAAwB,EAC3B,mBAAmB,EACnB,gBAAgB,EAChB,qBAAqB,EACrB,QAAQ,EACR,QAAQ,CACT,CAAC;EACFiE,QAAQ,EAAExF,MAAM,CAAC,UAAU,EAAE,CAAC,aAAa,CAAC;AAC9C,CAAC;AAEM,MAAMyF,gBAAsD,GAAG;EACpErD,aAAa,EAAE;IACbN,IAAI,EAAE9B,MAAM,CAAC,qBAAqB,EAAE,CAClC,4BAA4B,EAC5B,GAAGyB,yBAAyB,EAC5B,GAAGC,2BAA2B,EAC9B,GAAGf,eAAe,CACnB;GACF;EACD+E,KAAK,EAAE;IACL5D,IAAI,EAAE9B,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,EAAE,oBAAoB,CAAC,CAAC;IACnE+B,SAAS,EAAE/B,MAAM,CAAC,kBAAkB,EAAE,CACpC,yBAAyB,EACzB,GAAGiB,gCAAgC,CACpC,CAAC;IACF0E,OAAO,EAAE3F,MAAM,CAAC,gBAAgB,EAAE,CAAC,mBAAmB,CAAC,CAAC;IACxD4F,gBAAgB,EAAE5F,MAAM,CAAC,0BAA0B,EAAE,CACnD,iCAAiC,CAClC,CAAC;IACFgC,EAAE,EAAEhC,MAAM,CAAC,UAAU,EAAE,CAAC,aAAa,CAAC;GACvC;EAEDsC,WAAW,EAAE;IACXuD,MAAM,EAAE7F,MAAM,CAAC,IAAI,EAAE,CAAC,yBAAyB,CAAC;GACjD;EAED8F,MAAM,EAAE;IACNC,KAAK,EAAE/F,MAAM,CAAC,cAAc,EAAE,CAC5B,qBAAqB,EACrB,qBAAqB,CACtB;GACF;EAEDwC,IAAI,EAAE;IACJwD,GAAG,EAAEhG,MAAM,CAAC,UAAU,EAAE,CAAC,aAAa,CAAC;GACxC;EAEDiG,QAAQ,EAAE;IACRC,UAAU,EAAElG,MAAM,CAAC,sBAAsB,EAAE,CAAC,6BAA6B,CAAC,CAAC;IAC3EmG,aAAa,EAAEnG,MAAM,CAAC,yBAAyB,EAAE,CAC/C,gCAAgC,CACjC;GACF;EAEDkD,QAAQ,EAAE;IACRpB,IAAI,EAAE9B,MAAM,CAAC,eAAe,EAAE,CAC5B,sBAAsB,EACtB,GAAG2B,oBAAoB,EACvB,GAAGhB,eAAe,CACnB,CAAC;IACFoF,KAAK,EAAE/F,MAAM,CAAC,gBAAgB,EAAE,CAC9B,uBAAuB,EACvB,qBAAqB,CACtB;GACF;EAEDoG,IAAI,EAAE;IACJC,SAAS,EAAErG,MAAM,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC;IAClEsG,KAAK,EAAEtG,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;IACpEuG,OAAO,EAAEvG,MAAM,CAAC,eAAe,EAAE,CAC/B,sBAAsB,EACtB,kBAAkB,EAClB,kBAAkB,CACnB,CAAC;IACFwG,SAAS,EAAExG,MAAM,CAAC,gBAAgB,EAAE,CAAC,mBAAmB,CAAC,EAAE,WAAW;GACvE;EAEDyG,IAAI,EAAE;IACJC,WAAW,EAAE1G,MAAM,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC;IACpE2G,WAAW,EAAE3G,MAAM,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC;IACpE4G,KAAK,EAAE5G,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9C6G,KAAK,EAAE7G,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9C8G,KAAK,EAAE9G,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9C+G,IAAI,EAAE/G,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IAC3CgH,KAAK,EAAEhH,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAClDiH,KAAK,EAAEjH,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9CkH,IAAI,EAAElH,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IAC3CmH,OAAO,EAAEnH,MAAM,CAAC,cAAc,EAAE,CAAC,qBAAqB,CAAC,CAAC;IACxDoH,KAAK,EAAEpH,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9CqH,MAAM,EAAErH,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IACjDsH,QAAQ,EAAEtH,MAAM,CAAC,eAAe,EAAE,CAAC,sBAAsB,CAAC,CAAC;IAC3DuH,MAAM,EAAEvH,MAAM,CAAC,aAAa,EAAE,CAAC,oBAAoB,CAAC,CAAC;IACrDwH,KAAK,EAAExH,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9CyH,KAAK,EAAEzH,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAClD0H,IAAI,EAAE1H,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IAC3C2H,KAAK,EAAE3H,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAClD4H,KAAK,EAAE5H,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAClD6H,KAAK,EAAE7H,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9C8H,KAAK,EAAE9H,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9C+H,IAAI,EAAE/H,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IAC3CgI,OAAO,EAAEhI,MAAM,CAAC,cAAc,EAAE,CAAC,qBAAqB,CAAC,CAAC;IACxDiI,KAAK,EAAEjI,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,CAAC;IAClDkI,UAAU,EAAElI,MAAM,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC;IACnEmI,IAAI,EAAEnI,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IAC3CoI,OAAO,EAAEpI,MAAM,CAAC,cAAc,EAAE,CAAC,qBAAqB,CAAC,CAAC;IACxDqI,IAAI,EAAErI,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IAC3CsI,IAAI,EAAEtI,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IAC3CuI,KAAK,EAAEvI,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;IAC9CwI,KAAK,EAAExI,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC;GAClD;EAEDuD,GAAG,EAAE;IACHzB,IAAI,EAAE9B,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,GAAGmB,eAAe,CAAC,CAAC;IAC3DsH,OAAO,EAAEzI,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,GAAGmB,eAAe,CAAC,CAAC;IAC9DuH,KAAK,EAAE1I,MAAM,CAAC,IAAI,EAAE,CAAC,mBAAmB,EAAE,GAAGmB,eAAe,CAAC,CAAC;IAC9Da,EAAE,EAAEhC,MAAM,CAAC,IAAI,EAAE,CAAC,eAAe,EAAE,GAAGmB,eAAe,CAAC;GACvD;EAEDqC,MAAM,EAAE;IACNmF,OAAO,EAAE3I,MAAM,CAAC,gBAAgB,EAAE,CAAC,mBAAmB,CAAC,CAAC;IACxD4I,gBAAgB,EAAE5I,MAAM,CAAC,yBAAyB,EAAE,CAClD,4BAA4B,CAC7B,CAAC;IACF6I,gBAAgB,EAAE7I,MAAM,CAAC,yBAAyB,EAAE,CAClD,4BAA4B,CAC7B,CAAC;IACF8I,UAAU,EAAE9I,MAAM,CAAC,oBAAoB,EAAE,CAAC,2BAA2B,CAAC,CAAC;IACvE+I,QAAQ,EAAE/I,MAAM,CAAC,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,CAAC;IAC7DgJ,SAAS,EAAEhJ,MAAM,CAAC,mBAAmB,EAAE,CAAC,sBAAsB,CAAC,CAAC;IAChEiJ,KAAK,EAAEjJ,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC;IACpDkJ,aAAa,EAAElJ,MAAM,CAAC,wBAAwB,EAAE,CAC9C,2BAA2B,CAC5B,CAAC;IACFgF,UAAU,EAAEhF,MAAM,CAAC,oBAAoB,EAAE,CAAC,uBAAuB,CAAC,CAAC;IACnEiF,QAAQ,EAAEjF,MAAM,CAAC,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,CAAC;IAC7D+F,KAAK,EAAE/F,MAAM,CAAC,cAAc,EAAE,CAC5B,qBAAqB,EACrB,qBAAqB,CACtB;GACF;EAEDN,MAAM,EAAE;IACNyJ,MAAM,EAAEnJ,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC;IACrDoJ,MAAM,EAAEpJ,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC;IACrDqJ,gBAAgB,EAAErJ,MAAM,CAAC,0BAA0B,EAAE,CACnD,6BAA6B,CAC9B,CAAC;IACFsJ,cAAc,EAAEtJ,MAAM,CAAC,wBAAwB,EAAE,CAC/C,2BAA2B,CAC5B,CAAC;IACFuJ,OAAO,EAAEvJ,MAAM,CAAC,gBAAgB,EAAE,CAAC,mBAAmB,CAAC,CAAC;IACxDwJ,MAAM,EAAExJ,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC;IACrDyJ,WAAW,EAAEzJ,MAAM,CAAC,qBAAqB,EAAE,CACzC,wBAAwB,EACxB,mBAAmB,CACpB,CAAC;IACF0J,wBAAwB,EAAE1J,MAAM,CAAC,oCAAoC,EAAE,CACrE,uCAAuC,CACxC,CAAC;IACF2J,yBAAyB,EAAE3J,MAAM,CAAC,qCAAqC,EAAE,CACvE,wCAAwC,CACzC,CAAC;IACF4J,mBAAmB,EAAE5J,MAAM,CAAC,+BAA+B,EAAE,CAC3D,kCAAkC,CACnC,CAAC;IACF6J,qBAAqB,EAAE7J,MAAM,CAAC,iCAAiC,EAAE,CAC/D,WAAW,CACZ,CAAC;IACF8J,cAAc,EAAE9J,MAAM,CAAC,yBAAyB,EAAE,CAChD,4BAA4B,CAC7B,CAAC;IACFyI,OAAO,EAAEzI,MAAM,CAAC,iBAAiB,EAAE,CACjC,oBAAoB,EACpB,kBAAkB,CACnB,CAAC;IACF+J,MAAM,EAAE/J,MAAM,CAAC,gBAAgB,EAAE,CAAC,mBAAmB,CAAC,CAAC;IACvDgK,EAAE,EAAEhK,MAAM,CAAC,WAAW,EAAE,CAAC,cAAc,CAAC,CAAC;IACzCiK,YAAY,EAAEjK,MAAM,CAAC,sBAAsB,EAAE,CAAC,yBAAyB,CAAC,CAAC;IACzEkK,QAAQ,EAAElK,MAAM,CAAC,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,CAAC;IAC7DmK,QAAQ,EAAEnK,MAAM,CAAC,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,CAAC;IAC7DL,IAAI,EAAEK,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC/CoK,iBAAiB,EAAEpK,MAAM,CAAC,2BAA2B,EAAE,CACrD,8BAA8B,CAC/B,CAAC;IACFqK,IAAI,EAAErK,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC/CsK,cAAc,EAAEtK,MAAM,CAAC,yBAAyB,EAAE,CAChD,4BAA4B,CAC7B,CAAC;IACFuK,MAAM,EAAEvK,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC;GACrD;EAED0D,OAAO,EAAE;IACP8G,GAAG,EAAExK,MAAM,CAAC,IAAI,EAAEiB,gCAAgC,CAAC;IACnDwJ,UAAU,EAAEzK,MAAM,CAAC,IAAI,EAAE,CACvB,wBAAwB,EACxB,GAAGiB,gCAAgC,CACpC,CAAC;IACFyJ,GAAG,EAAE1K,MAAM,CAAC,IAAI,EAAE,CAChB,gBAAgB,EAChB,oBAAoB,EACpB,GAAGiB,gCAAgC,CACpC,CAAC;IACF0J,IAAI,EAAE3K,MAAM,CAAC,IAAI,EAAEiB,gCAAgC,CAAC;IACpD2J,GAAG,EAAE5K,MAAM,CAAC,IAAI,EAAE,CAAC,oBAAoB,EAAE,GAAGgB,mBAAmB,CAAC,CAAC;IACjE6J,aAAa,EAAE7K,MAAM,CAAC,IAAI,EAAE,CAC1B,2BAA2B,EAC3B,GAAGgB,mBAAmB,CACvB;GACF;EAED6C,OAAO,EAAE;IACPiH,KAAK,EAAE9K,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC;IACpD+K,SAAS,EAAE/K,MAAM,CAAC,mBAAmB,EAAE,CAAC,sBAAsB,CAAC,CAAC;IAChEgL,cAAc,EAAEhL,MAAM,CAAC,yBAAyB,EAAE,CAChD,gCAAgC,CACjC,CAAC;IACFsJ,cAAc,EAAEtJ,MAAM,CAAC,yBAAyB,EAAE,CAChD,4BAA4B,CAC7B,CAAC;IACFiL,cAAc,EAAEjL,MAAM,CAAC,yBAAyB,EAAE,CAChD,gCAAgC,CACjC,CAAC;IACFkL,cAAc,EAAElL,MAAM,CAAC,yBAAyB,EAAE,CAChD,4BAA4B,CAC7B,CAAC;IACFmL,GAAG,EAAEnL,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC9CoL,WAAW,EAAEpL,MAAM,CAAC,sBAAsB,EAAE,CAC1C,6BAA6B,CAC9B,CAAC;IACFqL,eAAe,EAAErL,MAAM,CAAC,2BAA2B,EAAE,CACnD,kCAAkC,CACnC,CAAC;IACFsL,cAAc,EAAEtL,MAAM,CAAC,0BAA0B,EAAE,CACjD,iCAAiC,CAClC,CAAC;IACFuL,kBAAkB,EAAEvL,MAAM,CAAC,+BAA+B,EAAE,CAC1D,sCAAsC,CACvC,CAAC;IACF0J,wBAAwB,EAAE1J,MAAM,CAAC,qCAAqC,EAAE,CACtE,wCAAwC,CACzC,CAAC;IACF8J,cAAc,EAAE9J,MAAM,CAAC,0BAA0B,EAAE,CACjD,6BAA6B,CAC9B,CAAC;IACFwL,GAAG,EAAExL,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC9CyL,WAAW,EAAEzL,MAAM,CAAC,sBAAsB,EAAE,CAC1C,6BAA6B,CAC9B,CAAC;IACF0L,cAAc,EAAE1L,MAAM,CAAC,0BAA0B,EAAE,CACjD,iCAAiC,CAClC,CAAC;IACFiK,YAAY,EAAEjK,MAAM,CAAC,uBAAuB,EAAE,CAAC,0BAA0B,CAAC,CAAC;IAC3E2L,QAAQ,EAAE3L,MAAM,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC;IACjE4L,OAAO,EAAE5L,MAAM,CAAC,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,CAAC;IAC5DoK,iBAAiB,EAAEpK,MAAM,CAAC,4BAA4B,EAAE,CACtD,+BAA+B,CAChC,CAAC;IACF6L,GAAG,EAAE7L,MAAM,CAAC,aAAa,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC9CsK,cAAc,EAAEtK,MAAM,CAAC,0BAA0B,EAAE,CACjD,6BAA6B,CAC9B;GACF;EAED8D,MAAM,EAAE;IACNe,MAAM,EAAE7E,MAAM,CAAC,eAAe,EAAE,CAAC,sBAAsB,CAAC;GACzD;EAEDR,GAAG,EAAE;IACHsC,IAAI,EAAE9B,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,GAAGoB,eAAe,CAAC,CAAC;IAC3DY,EAAE,EAAEhC,MAAM,CAAC,IAAI,EAAE,CAAC,eAAe,EAAE,GAAGoB,eAAe,CAAC;GACvD;EAED0K,MAAM,EAAE;IACNC,MAAM,EAAE/L,MAAM,CAAC,eAAe,EAAE,CAAC,sBAAsB,CAAC,CAAC;IACzDgM,MAAM,EAAEhM,MAAM,CAAC,eAAe,EAAE,CAC9B,sBAAsB,EACtB,2BAA2B,EAC3B,aAAa,CACd,CAAC;IACFiM,aAAa,EAAEjM,MAAM,CAAC,wBAAwB,EAAE,CAC9C,2BAA2B,CAC5B,CAAC;IACFkM,GAAG,EAAElM,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC;GAC5C;EAEDgE,MAAM,EAAE;IACNmI,YAAY,EAAEnM,MAAM,CAAC,sBAAsB,EAAE,CAC3C,6BAA6B,EAC7B,qCAAqC,CACtC,CAAC;IACFoM,aAAa,EAAEpM,MAAM,CAAC,uBAAuB,EAAE,CAC7C,0BAA0B,CAC3B,CAAC;IACFqM,OAAO,EAAErM,MAAM,CAAC,gBAAgB,EAAE,CAChC,uBAAuB,EACvB,yBAAyB,CAC1B,CAAC;IACFsM,GAAG,EAAEtM,MAAM,CAAC,YAAY,EAAE,EAAE,EAAE,WAAW,CAAC;IAC1CuM,WAAW,EAAEvM,MAAM,CAAC,qBAAqB,EAAE,CACzC,wBAAwB,EACxB,0BAA0B,CAC3B,CAAC;IACFwM,kBAAkB,EAAExM,MAAM,CAAC,6BAA6B,EAAE,CACxD,gCAAgC,EAChC,iBAAiB,CAClB,CAAC;IACFyM,YAAY,EAAEzM,MAAM,CAAC,sBAAsB,EAAE,CAC3C,6BAA6B,EAC7B,WAAW,CACZ,CAAC;IACF0M,kBAAkB,EAAE1M,MAAM,CAAC,6BAA6B,EAAE,CACxD,oCAAoC,EACpC,WAAW,CACZ,CAAC;IACF2M,WAAW,EAAE3M,MAAM,CAAC,sBAAsB,EAAE,CAC1C,6BAA6B,EAC7B,WAAW,CACZ,CAAC;IACF4M,iBAAiB,EAAE5M,MAAM,CAAC,6BAA6B,EAAE,CACvD,oCAAoC,EACpC,WAAW,CACZ,CAAC;IACF6M,QAAQ,EAAE7M,MAAM,CAAC,iBAAiB,EAAE,CAClC,oBAAoB,EACpB,GAAGa,sBAAsB,CAC1B,CAAC;IACFiM,MAAM,EAAE9M,MAAM,CAAC,gBAAgB,EAAE,EAAE,EAAE,WAAW,CAAC;IACjD+M,KAAK,EAAE/M,MAAM,CAAC,cAAc,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;IACrEgN,OAAO,EAAEhN,MAAM,CAAC,gBAAgB,EAAE,CAAC,uBAAuB,CAAC,CAAC;IAC5DiN,QAAQ,EAAEjN,MAAM,CAAC,kBAAkB,EAAE,CACnC,qBAAqB,EACrB,qBAAqB,CACtB,CAAC;IACF2L,QAAQ,EAAE3L,MAAM,CAAC,iBAAiB,EAAE4B,6BAA6B,CAAC;IAClEsL,WAAW,EAAElN,MAAM,CAAC,qBAAqB,EAAE,CAAC,4BAA4B,CAAC,CAAC;IAC1EmN,UAAU,EAAEnN,MAAM,CAAC,mBAAmB,EAAE,CAAC,0BAA0B,CAAC,CAAC;IACrEoN,YAAY,EAAEpN,MAAM,CAAC,sBAAsB,EAAE,CAC3C,6BAA6B,CAC9B,CAAC;IACFqN,OAAO,EAAErN,MAAM,CAAC,gBAAgB,EAAE,CAChC,mBAAmB,EACnB,mBAAmB,CACpB,CAAC;IACFsN,MAAM,EAAEtN,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;IACzEuN,OAAO,EAAEvN,MAAM,CAAC,gBAAgB,EAAE,CAChC,mBAAmB,EACnB,kBAAkB,CACnB,CAAC;IACFwN,KAAK,EAAExN,MAAM,CAAC,cAAc,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;IACrEyN,WAAW,EAAEzN,MAAM,CAAC,qBAAqB,EAAE,CACzC,wBAAwB,EACxB,sBAAsB,CACvB,CAAC;IACF0N,WAAW,EAAE1N,MAAM,CAAC,sBAAsB,EAAE,CAC1C,yBAAyB,EACzB,qBAAqB,EACrB,uBAAuB,EACvB,uBAAuB,CACxB,CAAC;IACF2N,WAAW,EAAE3N,MAAM,CAAC,oBAAoB,EAAE,CAAC,uBAAuB,CAAC;GACpE;EAEDoE,GAAG,EAAE;IACHwJ,QAAQ,EAAE5N,MAAM,CAAC,eAAe,EAAE,CAAC,mBAAmB,EAAE,SAAS,CAAC;GACnE;EAEDsE,OAAO,EAAE;IACPxC,IAAI,EAAE9B,MAAM,CAAC,IAAI,EAAE,CAAC,sBAAsB,EAAE,GAAGqB,mBAAmB,CAAC,CAAC;IACpEW,EAAE,EAAEhC,MAAM,CAAC,IAAI,EAAE,CAAC,oBAAoB,EAAE,GAAGqB,mBAAmB,CAAC;GAChE;EAEDkD,OAAO,EAAE;IACPzC,IAAI,EAAE9B,MAAM,CAAC,IAAI,EAAE,CAAC,sBAAsB,EAAE,GAAGsB,mBAAmB,CAAC,CAAC;IACpEU,EAAE,EAAEhC,MAAM,CAAC,IAAI,EAAE,CAAC,oBAAoB,EAAE,GAAGsB,mBAAmB,CAAC;GAChE;EAEDyB,SAAS,EAAElB,uBAAuB;EAClCsB,UAAU,EAAE;IACV0K,UAAU,EAAE7N,MAAM,CAAC,IAAI,EAAE,CACvB,gCAAgC,EAChC,GAAGS,sBAAsB,CAC1B,CAAC;IACFqN,OAAO,EAAE9N,MAAM,CAAC,IAAI,EAAE,CACpB,6BAA6B,EAC7B,GAAGS,sBAAsB,CAC1B,CAAC;IACF,GAAGoB;GACJ;EACDuB,iBAAiB,EAAEvB,uBAAuB;EAC1CmB,UAAU,EAAEnB,uBAAuB;EACnCwB,WAAW,EAAExB,uBAAuB;EACpCoB,UAAU,EAAEpB,uBAAuB;EACnCyB,WAAW,EAAEzB,uBAAuB;EACpCgB,YAAY,EAAEhB,uBAAuB;EACrCiB,YAAY,EAAEjB,uBAAuB;EAErCkM,WAAW,EAAE;IACXC,YAAY,EAAEhO,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;IAC7CmN,SAAS,EAAEjO,MAAM,CAAC,IAAI,EAAEc,iBAAiB,CAAC;IAC1CoN,YAAY,EAAElO,MAAM,CAAC,IAAI,EAAEc,iBAAiB;;AAEhD,CAAC;AAEM,MAAMqN,kBAAkB,GAAG;EAChCC,cAAc,EAAEpO,MAAM,CAAC,yBAAyB,EAAE,CAChD,wCAAwC,EACxC,GAAGyB,yBAAyB,EAC5B,kCAAkC,EAClC,GAAGE,oBAAoB,CACxB,CAAC;EACF0M,EAAE,EAAErO,MAAM,CAAC,aAAa,EAAE;;;;;;;EAOxB,kBAAkB,EAClB,0BAA0B,EAC1B,aAAa,CACd,CAAC;EACFsO,MAAM,EAAEtO,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC;EAC1CuO,GAAG,EAAEvO,MAAM,CAAC,IAAI,EAAE,CAAC,eAAe,CAAC,CAAC;EACpCwO,IAAI,EAAExO,MAAM,CAAC,eAAe,EAAE,CAAC,kBAAkB,CAAC,CAAC;EACnDyO,KAAK,EAAEzO,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,CAAC,CAAC;EACxC0O,IAAI,EAAE1O,MAAM,CAAC,IAAI,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACtC2O,WAAW,EAAE3O,MAAM,CAAC,wBAAwB,EAAE,CAAC,yBAAyB,CAAC,CAAC;EAC1E4O,UAAU,EAAE5O,MAAM,CAAC,sBAAsB,EAAE,CAAC,2BAA2B,CAAC,CAAC;EACzE6O,MAAM,EAAE7O,MAAM,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,CAAC,EAAE8O,SAAS,EAAE,CAAC,QAAQ,CAAC,CAAC;EAC7EC,UAAU,EAAE/O,MAAM,CAAC,sBAAsB,EAAE,CAAC,sBAAsB,CAAC,CAAC;EACpEgP,WAAW,EAAEhP,MAAM,CAAC,sBAAsB,EAAE,CAAC,6BAA6B,CAAC,CAAC;EAC5EiP,WAAW,EAAEjP,MAAM,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAC;EACjEkP,MAAM,EAAElP,MAAM,CAAC,IAAI,EAAE,CAAC,mBAAmB,CAAC,CAAC;EAC3CmP,IAAI,EAAEnP,MAAM,CAAC,IAAI,EAAE,CACjB,4BAA4B,EAC5B,GAAGyB,yBAAyB,EAC5B,sBAAsB,EACtB,GAAGE,oBAAoB,CACxB,CAAC;EACFyN,OAAO,EAAEpP,MAAM,CAAC,kBAAkB,EAAE,CAClC,oBAAoB,EACpB,yBAAyB,CAC1B,CAAC;EACFqP,QAAQ,EAAErP,MAAM,CAAC,oBAAoB,EAAE,CAAC,qBAAqB,CAAC,CAAC;EAC/DuJ,OAAO,EAAEvJ,MAAM,CAAC,kBAAkB,EAAEY,2BAA2B,CAAC;EAChE0O,KAAK,EAAEtP,MAAM,CAAC,gBAAgB,EAAE,CAC9B,gBAAgB,EAChB,6BAA6B;;;;;;EAM7B,uBAAuB,EACvB,GAAG2B,oBAAoB,CACxB,CAAC;EACF4N,IAAI,EAAEvP,MAAM,CAAC,IAAI,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACtCwP,IAAI,EAAExP,MAAM,CAAC,eAAe,EAAE,CAAC,eAAe,CAAC,CAAC;EAChDyP,MAAM,EAAEzP,MAAM,CAAC,iBAAiB,EAAE,CAChC,iBAAiB,EACjB,8BAA8B,EAC9B,wBAAwB,EACxB,GAAG2B,oBAAoB,CACxB,CAAC;EACF+N,YAAY,EAAE1P,MAAM,CAAC,uBAAuB,EAAE,CAAC,4BAA4B,CAAC,CAAC;EAC7E2P,OAAO,EAAE3P,MAAM,CAAC,IAAI,EAAE,CAAC,oBAAoB,EAAE,GAAGgB,mBAAmB,CAAC,CAAC;EACrE4O,IAAI,EAAE5P,MAAM,CAAC,eAAe,EAAE,CAC5B,eAAe,EACf,4BAA4B,EAC5B,sBAAsB,EACtB,GAAG2B,oBAAoB,CACxB,CAAC;EACFkO,SAAS,EAAE7P,MAAM,CAAC,qBAAqB,EAAE,CAAC,qBAAqB,CAAC,CAAC;EACjE8P,QAAQ,EAAE9P,MAAM,CAAC,oBAAoB,EAAE,CAAC,oBAAoB,CAAC,CAAC;EAC9D+P,aAAa,EAAE/P,MAAM,CAAC,0BAA0B,EAAE,CAChD,0BAA0B,CAC3B,CAAC;EACFgQ,KAAK,EAAEhQ,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,CAAC,CAAC;EACxCiQ,KAAK,EAAEjQ,MAAM,CAAC,gBAAgB,EAAE,CAAC,iBAAiB,CAAC,CAAC;EACpDkQ,OAAO,EAAElQ,MAAM,CAAC,mBAAmB,EAAE,CACnC,mBAAmB,EACnB,+BAA+B,EAC/B,gCAAgC,EAChC,0BAA0B,EAC1B,GAAG2B,oBAAoB,CACxB,CAAC;EACFwO,IAAI,EAAEnQ,MAAM,CAAC,eAAe,EAAE,CAAC,eAAe,EAAE,2BAA2B,CAAC,CAAC;EAC7EoQ,UAAU,EAAEpQ,MAAM,CAAC,IAAI,EAAE,CACvB,8BAA8B,EAC9B,GAAGiC,oBAAoB,CACxB,CAAC;EACFoO,eAAe,EAAErQ,MAAM,CAAC,IAAI,EAAE,CAC5B,oCAAoC,EACpC,GAAGiC,oBAAoB,CACxB,CAAC;EACFqO,OAAO,EAAEtQ,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC;EAC3CuQ,KAAK,EAAEvQ,MAAM,CAAC,gBAAgB,EAAE,CAAC,oBAAoB,CAAC,CAAC;EACvDyI,OAAO,EAAEzI,MAAM,CAAC,mBAAmB,EAAE,CAAC,uBAAuB,CAAC,CAAC;EAC/DwQ,YAAY,EAAExQ,MAAM,CAAC,0BAA0B,EAAE,CAC/C,8BAA8B,EAC9B,QAAQ,EACR,qBAAqB,CACtB,CAAC;EACFyQ,UAAU,EAAEzQ,MAAM,CAAC,uBAAuB,EAAE,CAC1C,2BAA2B,EAC3B,QAAQ,EACR,qBAAqB,CACtB,CAAC;EACF0Q,SAAS,EAAE1Q,MAAM,CAAC,IAAI,EAAE,CAAC,qBAAqB,CAAC,CAAC;EAChD2Q,QAAQ,EAAE3Q,MAAM,CAAC,IAAI,EAAE,CAAC,oBAAoB,CAAC,CAAC;EAC9CH,OAAO,EAAEG,MAAM,CAAC,mBAAmB,EAAE,CACnC,mBAAmB,EACnB,gCAAgC,EAChC,0BAA0B,EAC1B,GAAG2B,oBAAoB,EACvB,8BAA8B,CAC/B,CAAC;EACFiP,QAAQ,EAAE5Q,MAAM,CAAC,mBAAmB,EAAE,CACpC,mBAAmB,EACnB,oBAAoB,CACrB,CAAC;EACF6Q,OAAO,EAAE7Q,MAAM,CAAC,IAAI,EAAE,CACpB,+BAA+B,EAC/B,GAAGyB,yBAAyB,EAC5B,yBAAyB,EACzB,GAAGE,oBAAoB,CACxB,CAAC;EACFmP,OAAO,EAAE9Q,MAAM,CAAC,mBAAmB,EAAE,CAAC,mBAAmB,CAAC,CAAC;EAC3D+Q,YAAY,EAAE/Q,MAAM,CAAC,yBAAyB,EAAE,CAAC,0BAA0B,CAAC,CAAC;EAC7EgR,MAAM,EAAEhR,MAAM,CAAC,IAAI,EAAE,CAAC,mBAAmB,CAAC,CAAC;EAC3CiR,IAAI,EAAEjR,MAAM,CAAC,IAAI,EAAE,CAAC,eAAe,CAAC,CAAC;EACrCL,IAAI,EAAEK,MAAM,CAAC,eAAe,EAAEY,2BAA2B,CAAC;EAC1DsQ,SAAS,EAAElR,MAAM,CAAC,IAAI,EAAE,CAAC,yBAAyB,CAAC,CAAC;EACpDmR,WAAW,EAAEnR,MAAM,CAAC,wBAAwB,EAAE,CAAC,wBAAwB,CAAC,CAAC;EACzEoR,QAAQ,EAAEpR,MAAM,CAAC,IAAI,EAAE,CAAC,wBAAwB,CAAC,CAAC;EAClDqR,IAAI,EAAErR,MAAM,CAAC,IAAI,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACtCsR,GAAG,EAAEtR,MAAM,CAAC,cAAc,EAAE,CAC1B,cAAc,EACd,2BAA2B,EAC3B,qBAAqB,CACtB,CAAC;EACF+M,KAAK,EAAE/M,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;EAC1DiN,QAAQ,EAAEjN,MAAM,CAAC,oBAAoB,EAAE,CACrC,qBAAqB,EACrB,gBAAgB,CACjB,CAAC;EACFF,IAAI,EAAEE,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC;EACxCuR,MAAM,EAAEvR,MAAM,CAAC,kBAAkB,EAAE,CAAC,mBAAmB,CAAC,CAAC;EACzDwR,QAAQ,EAAExR,MAAM,CAAC,oBAAoB,EAAE,CAAC,qBAAqB,CAAC,CAAC;EAC/DyR,IAAI,EAAEzR,MAAM,CAAC,eAAe,EAAE,CAAC,eAAe,CAAC,CAAC;EAChD0R,MAAM,EAAE1R,MAAM,CAAC,iBAAiB,EAAE,CAChC,iBAAiB,EACjB,8BAA8B,EAC9B,wBAAwB,EACxB,GAAG2B,oBAAoB,CACxB,CAAC;EACFgQ,WAAW,EAAE3R,MAAM,CAAC,uBAAuB,EAAE,CAAC,uBAAuB,CAAC,CAAC;EACvE4R,MAAM,EAAE5R,MAAM,CAAC,iBAAiB,EAAE,CAAC,kBAAkB,CAAC,CAAC;EACvDqN,OAAO,EAAErN,MAAM,CAAC,IAAI,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;EAC9D6R,UAAU,EAAE7R,MAAM,CAAC,sBAAsB,EAAE,CACzC,uBAAuB,EACvB,mBAAmB,EACnB,gBAAgB,CACjB,CAAC;EACF8R,OAAO,EAAE9R,MAAM,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,CAAC,CAAC;EACzDsN,MAAM,EAAEtN,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;EAC5D+R,UAAU,EAAE/R,MAAM,CAAC,IAAI,EAAE,CACvB,8BAA8B,EAC9B,GAAGiC,oBAAoB,CACxB,CAAC;EACF+P,eAAe,EAAEhS,MAAM,CAAC,IAAI,EAAE,CAC5B,oCAAoC,EACpC,GAAGiC,oBAAoB,CACxB,CAAC;EACFgQ,OAAO,EAAEjS,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC;EAC3CkS,KAAK,EAAElS,MAAM,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACnDmS,KAAK,EAAEnS,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,CAAC,CAAC;EACxCoS,IAAI,EAAEpS,MAAM,CAAC,eAAe,EAAE,CAC5B,eAAe,EACf,4BAA4B,EAC5B,sBAAsB,EACtB,GAAG2B,oBAAoB,CACxB,CAAC;EACFvB,IAAI,EAAEJ,MAAM,CAAC,eAAe,EAAE,CAAC,eAAe,CAAC,CAAC;EAChDqS,MAAM,EAAErS,MAAM,CAAC,iBAAiB,EAAE,CAAC,iBAAiB,CAAC,CAAC;EACtDwN,KAAK,EAAExN,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;EAC1DsS,UAAU,EAAEtS,MAAM,CAAC,sBAAsB,EAAE,CAAC,uBAAuB,CAAC,CAAC;EACrEuS,MAAM,EAAEvS,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC;EAC1CwS,MAAM,EAAExS,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC;EAC1CyS,GAAG,EAAEzS,MAAM,CAAC,IAAI,EAAE,CAAC,eAAe,CAAC,CAAC;EACpC0S,MAAM,EAAE1S,MAAM,CAAC,IAAI,EAAE,CAAC,kBAAkB,CAAC,CAAC;EAC1C2S,GAAG,EAAE3S,MAAM,CAAC,IAAI,EAAE,CAAC,eAAe,CAAC,CAAC;EACpC4S,IAAI,EAAE5S,MAAM,CAAC,IAAI,EAAE,CACjB,4BAA4B,EAC5B,GAAGyB,yBAAyB,EAC5B,sBAAsB,EACtB,GAAGE,oBAAoB,CACxB,CAAC;EACFkR,IAAI,EAAE7S,MAAM,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;EACxD8S,OAAO,EAAE9S,MAAM,CAAC,IAAI,EAAE,CACpB,gCAAgC,EAChC,GAAGyB,yBAAyB,EAC5B,0BAA0B,EAC1B,GAAGE,oBAAoB,CACxB,CAAC;EACFoR,OAAO,EAAE/S,MAAM,CAAC,IAAI,EAAE,CACpB,0BAA0B,EAC1B,GAAG2B,oBAAoB,EACvB,GAAGF,yBAAyB,EAC5B,GAAGC,2BAA2B,CAC/B,CAAC;EACFsR,aAAa,EAAEhT,MAAM,CAAC,IAAI,EAAE,CAAC,0BAA0B,CAAC,CAAC;EACzDiT,OAAO,EAAEjT,MAAM,CAAC,IAAI,EAAE,CAAC,oBAAoB,CAAC,CAAC;EAC7CkT,WAAW,EAAElT,MAAM,CAAC,IAAI,EAAE,CAAC,uBAAuB,CAAC,CAAC;EACpDmT,WAAW,EAAEnT,MAAM,CAAC,IAAI,EAAE,CAAC,uBAAuB,CAAC,CAAC;EACpDoT,MAAM,EAAEpT,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;EAC5DqT,WAAW,EAAErT,MAAM,CAAC,IAAI,EAAE,CAAC,wBAAwB,CAAC,CAAC;EACrDsT,UAAU,EAAEtT,MAAM,CAAC,sBAAsB,EAAE,CAAC,sBAAsB,CAAC,CAAC;EACpEuT,QAAQ,EAAEvT,MAAM,CAAC,oBAAoB,EAAE,CACrC,oBAAoB,EACpB,eAAe,CAChB,CAAC;EACFwT,SAAS,EAAExT,MAAM,CAAC,qBAAqB,EAAE,CAAC,qBAAqB,CAAC,CAAC;EACjEyT,QAAQ,EAAEzT,MAAM,CAAC,IAAI,EAAE,CACrB,qBAAqB,EACrB,oBAAoB,EACpB,mBAAmB,EACnB,qBAAqB,CACtB,CAAC;EACF0T,YAAY,EAAE1T,MAAM,CAAC,yBAAyB,EAAE,CAAC,0BAA0B,CAAC,CAAC;EAC7E2T,IAAI,EAAE3T,MAAM,CAAC,eAAe,EAAE,CAAC,gBAAgB,CAAC,CAAC;EACjD4T,OAAO,EAAE5T,MAAM,CAAC,mBAAmB,EAAE,CAAC,oBAAoB,CAAC,CAAC;EAC5D6T,QAAQ,EAAE7T,MAAM,CAAC,oBAAoB,EAAE,CAAC,sBAAsB,CAAC,CAAC;EAChE8T,SAAS,EAAE9T,MAAM,CAAC,qBAAqB,EAAE,CAAC,oBAAoB,CAAC,CAAC;EAChE+T,SAAS,EAAE/T,MAAM,CAAC,qBAAqB,EAAE,CAAC,sBAAsB,CAAC,CAAC;EAClEgU,QAAQ,EAAEhU,MAAM,CAAC,oBAAoB,EAAE,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC;EAC5EiU,OAAO,EAAEjU,MAAM,CAAC,kBAAkB,EAAE,CAAC,kBAAkB,CAAC,CAAC;EACzDkU,MAAM,EAAElU,MAAM,CAAC,kBAAkB,EAAE,CAAC,yBAAyB,CAAC,CAAC;EAC/DuK,MAAM,EAAEvK,MAAM,CAAC,iBAAiB,EAAEY,2BAA2B,CAAC;EAC9DuT,IAAI,EAAEnU,MAAM,CAAC,eAAe,EAAE,CAAC,eAAe,CAAC,CAAC;EAChDoU,gBAAgB,EAAEpU,MAAM,CAAC,IAAI,EAAE,CAAC,yBAAyB,CAAC,CAAC;EAC3DqU,gBAAgB,EAAErU,MAAM,CAAC,IAAI,EAAE,CAAC,yBAAyB,CAAC,CAAC;EAC3DsU,gBAAgB,EAAEtU,MAAM,CAAC,IAAI,EAAE,CAAC,yBAAyB,CAAC,CAAC;EAC3DuU,gBAAgB,EAAEvU,MAAM,CAAC,IAAI,EAAE,CAAC,yBAAyB,CAAC,CAAC;EAC3D,CAAC,WAAW,GAAGA,MAAM,CAAC,IAAI,EAAE,CAAC,iBAAiB,CAAC;AACjD,CAAC;;;ECx8BQwU,KAAK,EAAIC;AAAC,IAAAC,MAAA,CAAAC,OAAA,IAAAD,MAAA;AAEJ,SAASE,eAAeA,CACrCC,IAA8B,EAC9BC,IAAc,EACd;EACA,MAAM;IAAEC,IAAI;IAAEC;GAAQ,GAAGF,IAAI;EAC7B,QAAQD,IAAI,CAAC/U,IAAI;IACf,KAAK,iBAAiB;MAAE;QACtB,IAAI,CAAC2U,GAAC,CAACQ,gBAAgB,CAACD,MAAM,EAAE;UAAEE,MAAM,EAAEH;SAAM,CAAC,EAAE,OAAO,KAAK;QAC/D,IAAIC,MAAM,CAACG,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;QAC5C,MAAMC,QAAQ,GAAGL,MAAM,CAACG,SAAS,CAAC,CAAC,CAAC;QACpC,OAAOV,GAAC,CAACa,eAAe,CAACD,QAAQ,CAAC,IAAIZ,GAAC,CAACc,iBAAiB,CAACF,QAAQ,CAAC;;;AAGzE;;;EChBSb,KAAK,EAAIC;AAAC,IAAAC,MAAA,CAAAC,OAAA,IAAAD,MAAA;AAGZ,MAAMc,aAAa,GAAG,wBAAwB;AAE9C,SAASC,UAAUA,CAACX,IAAS,EAAEY,EAAgB,EAAE;EACtD,MAAM;IAAEC;GAAQ,GAAGb,IAAI,CAACC,IAAI;EAE5B,IAAIa,QAAQ,EAAEC,QAAQ;EACtB,IAAIpB,GAAC,CAACqB,YAAY,CAACH,MAAM,CAAC,EAAE;IAC1BC,QAAQ,GAAGD,MAAM;IACjBE,QAAQ,GAAGpB,GAAC,CAACsB,SAAS,CAACJ,MAAM,CAAC;GAC/B,MAAM;IACLC,QAAQ,GAAGd,IAAI,CAACkB,KAAK,CAACC,6BAA6B,CAAC,SAAS,CAAC;IAC9DJ,QAAQ,GAAGpB,GAAC,CAACyB,oBAAoB,CAAC,GAAG,EAAEzB,GAAC,CAACsB,SAAS,CAACH,QAAQ,CAAC,EAAED,MAAM,CAAC;;EAGvEb,IAAI,CAACqB,WAAW,CACd1B,GAAC,CAAC2B,gBAAgB,CAAC3B,GAAC,CAAC4B,cAAc,CAACX,EAAE,EAAE,CAACG,QAAQ,CAAC,CAAC,EAAEpB,GAAC,CAAC6B,UAAU,CAAC,MAAM,CAAC,CAC3E,CAAC;EAEDxB,IAAI,CAACyB,UAAU,CAACC,gBAAgB,CAAC,WAAW,EAAEZ,QAAQ,CAAC;AACzD;AAEO,SAASa,cAAcA,CAACC,MAAc,EAAE;EAC7C,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9BA,MAAM,GAAGA,MAAM,CACZrJ,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBA,OAAO,CAAC,wBAAwB,EAAE,EAAE,CAAC,CACrCsJ,WAAW,EAAE;;EAGlB,OACEjX,MAAM,CAACkX,SAAS,CAACC,cAAc,CAACC,IAAI,CAACC,aAAa,EAAEL,MAAM,CAAC,IAC3DK,aAAa,CAACL,MAAM,CAAC;AAEzB;AAEO,SAASM,YAAYA,CAAClX,IAAY,EAAE;EACzC,OAAQ,mBAAkBA,IAAK,KAAI;AACrC;AAEO,SAASmX,gBAAgBA,CAC9BnX,IAAY,EACZoX,eAAwB,EACxBC,GAAW,EACX;EACA,OAAOD,eAAe,GACjB,GAAE1B,aAAc,YAAW1V,IAAK,GAAEqX,GAAI,EAAC,GACvC,yBAAwBrX,IAAK,KAAI;AACxC;;ACrC8C;EAGrC0U,KAAK,EAAIC;AAAC,IAAAC,MAAA,CAAAC,OAAA,IAAAD,MAAA;AAWnB,MAAM0C,eAAe,GAAG,iDAAiD;AACzE,MAAMC,aAAa,GAAG,8CAA8C;AAcpE,MAAMC,aAAa,GAAG,CACpB,OAAO,EACP,QAAQ,EAER,UAAU,EACV,gBAAgB,EAChB,iBAAiB,CAClB,CAAChG,GAAG,CAACiG,CAAC,IAAI,IAAIzT,MAAM,CAAE,YAAWyT,CAAE,OAAM,CAAC,CAAC;AAE5C,MAAMC,cAAc,GAAGA,CACrB1X,IAAY,EACZ2X,EAA6B,KACjB;EACZ,IAAIA,EAAE,CAAC3X,IAAI,CAAC,EAAE,OAAO,IAAI;EACzB,IAAI,CAACA,IAAI,CAACwS,UAAU,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;EACzC,MAAMoF,QAAQ,GAAI,UAAS5X,IAAI,CAACoS,KAAK,CAAC,CAAC,CAAE,EAAC;EAC1C,IAAI,CAACtS,gBAAgB,CAAC8X,QAAQ,CAAC,EAAE,OAAO,KAAK;EAC7C,OAAOD,EAAE,CAACC,QAAQ,CAAC;AACrB,CAAC;AAED,YAAeC,cAAc,CAAU,UACrC;EAAEC,QAAQ;EAAEC,MAAM;EAAEC,oBAAoB;EAAEC,kBAAkB;EAAEC,KAAK;EAAEC;AAAM,CAAC,EAC5E;EACEC,OAAO,GAAG,CAAC;EACXC,SAAS;EACTC,gBAAgB;EAChB,CAAChB,eAAe,GAAG;IAAEiB,aAAa,GAAG;GAAO,GAAG,EAAE;EACjD,CAAChB,aAAa,GAAG;IAAEH,eAAe,GAAG,KAAK;IAAEC,GAAG,GAAG;GAAO,GAAG;AAC9D,CAAC,EACD;EACA,MAAMmB,SAAS,GAAGL,KAAK,CAACM,MAAM,CAACA,MAAM,IAAI,CAAAA,MAAM,oBAANA,MAAM,CAAEzY,IAAI,MAAK,cAAc,CAAC;EAEzE,MAAM0Y,OAAO,GAAGT,kBAAkB,CAAC;IACjC7X,MAAM,EAAEgC,QAAQ;IAChBuW,MAAM,EAAEhT,gBAAgB;IACxBiT,QAAQ,EAAEvK;GACX,CAAC;EAEF,MAAMwK,SAAS,GAAG,IAAInZ,GAAG,CAACoZ,8BAA8B,CAACV,OAAO,CAAC,CAAC;EAElE,SAASW,iBAAiBA,CAACC,eAAe,EAAE;IAC1C,OAAO5B,eAAe,GAClB4B,eAAe,GACZ,GAAEtD,aAAc,UAAS,GACzB,GAAEA,aAAc,iBAAgB,GACnCsD,eAAe,GACf,uBAAuB,GACvB,qBAAqB;;EAG3B,SAASC,qBAAqBA,CAACjZ,IAAY,EAAEkZ,KAAK,EAAE;IAClD,IAAIlB,oBAAoB,CAAChY,IAAI,CAAC,EAAE;MAC9BkY,KAAK,CAAClY,IAAI,CAAC;MACXkZ,KAAK,CAACC,kBAAkB,CAACjC,YAAY,CAAClX,IAAI,CAAC,EAAEA,IAAI,CAAC;MAClD,OAAO,IAAI;;IAEb,OAAO,KAAK;;EAGd,SAASoZ,iBAAiBA,CAACC,KAAe,EAAEH,KAAK,EAAEtB,QAAQ,GAAG,IAAI,EAAE;IAClE,KAAK,MAAM5X,IAAI,IAAIqZ,KAAK,EAAE;MACxB,IAAIzB,QAAQ,EAAE;QACZF,cAAc,CAAC1X,IAAI,EAAEA,IAAI,IAAIiZ,qBAAqB,CAACjZ,IAAI,EAAEkZ,KAAK,CAAC,CAAC;OACjE,MAAM;QACLD,qBAAqB,CAACjZ,IAAI,EAAEkZ,KAAK,CAAC;;;;EAKxC,SAASI,eAAeA,CACtBvE,IAA8B,EAC9BwE,IAAI,EACJL,KAAK,EACLrD,MAAO,EACP;IACA,IACEd,IAAI,CAAC5U,IAAI,IACT,EAAE0V,MAAM,IAAId,IAAI,CAAC1U,OAAO,IAAI0U,IAAI,CAAC1U,OAAO,CAACyQ,QAAQ,CAAC+E,MAAM,CAAC,CAAC,IAC1D6B,cAAc,CAAC3C,IAAI,CAAC/U,IAAI,EAAEgY,oBAAoB,CAAC,EAC/C;MACA,MAAM;QAAEhY;OAAM,GAAG+U,IAAI;MACrB,IAAIiE,eAAe,GAAG,KAAK;MAC3B,IAAIX,SAAS,IAAKC,gBAAgB,IAAItY,IAAI,CAACwS,UAAU,CAAC,SAAS,CAAE,EAAE;QACjEwG,eAAe,GAAG,IAAI;OACvB,MAAM,IAAIhZ,IAAI,CAACwS,UAAU,CAAC,KAAK,CAAC,IAAI,CAACqG,SAAS,CAACnN,GAAG,CAAC1L,IAAI,CAAC,EAAE;QACzDgZ,eAAe,GAAG,IAAI;;MAExB,MAAMQ,cAAc,GAAGT,iBAAiB,CAACC,eAAe,CAAC;MACzD,OAAOE,KAAK,CAACO,mBAAmB,CAC7B,GAAED,cAAe,IAAGzE,IAAI,CAAC5U,IAAK,GAAEkX,GAAI,EAAC,EACtCkC,IACF,CAAC;;;EAIL,SAASG,eAAeA,CAAC1Z,IAAI,EAAE;IAC7B,IAAIA,IAAI,CAACwS,UAAU,CAAC,SAAS,CAAC,EAAE;MAC9B,MAAMmH,MAAM,GAAI,MAAK3Z,IAAI,CAACoS,KAAK,CAAC,CAAC,CAAE,EAAC;;;MAGpC,OAAOuH,MAAM,IAAI7Z,gBAAgB;;IAEnC,OAAO,IAAI;;EAGb,OAAO;IACLE,IAAI,EAAE,SAAS;IAEf4Z,WAAW,EAAErB,aAAa,GAAG,IAAI,GAAG7C,aAAa;IAEjDmE,SAAS,EAAE/Z,gBAAgB;IAE3Bga,eAAeA,CAAC9Z,IAAI,EAAE;MACpB,IAAI,CAAC6Y,SAAS,CAACnN,GAAG,CAAC1L,IAAI,CAAC,EAAE,OAAO,KAAK;MACtC,IAAIqY,SAAS,IAAIN,MAAM,KAAK,cAAc,EAAE,OAAO,IAAI;MACvD,IAAIO,gBAAgB,IAAIyB,2BAA2B,CAACrO,GAAG,CAAC1L,IAAI,CAAC,EAAE;QAC7D,OAAO,IAAI;;MAEb,OAAO0Z,eAAe,CAAC1Z,IAAI,CAAC;KAC7B;IAEDga,WAAWA,CAACC,IAAI,EAAEf,KAAK,EAAElE,IAAI,EAAE;MAC7B,IAAIiF,IAAI,CAACC,IAAI,KAAK,QAAQ,EAAE;MAE5B,MAAMxZ,OAAO,GAAGiW,cAAc,CAACsD,IAAI,CAACrD,MAAM,CAAC;MAC3C,IAAI,CAAClW,OAAO,EAAE;MAEd,IACEA,OAAO,CAAC4U,MAAM,KAAK,CAAC,IACpB2E,IAAI,CAACrD,MAAM,KAAKM,YAAY,CAACxW,OAAO,CAAC,CAAC,CAAC,CAAC,IACxCsX,oBAAoB,CAACtX,OAAO,CAAC,CAAC,CAAC,CAAC,EAChC;;;QAGAwX,KAAK,CAAC,IAAI,CAAC;QACX;;MAGF,MAAMiC,UAAU,GAAG,IAAIza,GAAG,CAACgB,OAAO,CAAC;MACnC,MAAM0Z,eAAe,GAAG1Z,OAAO,CAACiP,MAAM,CAAC0K,MAAM,IAAI;QAC/C,IAAI,CAACA,MAAM,CAAC7H,UAAU,CAAC,SAAS,CAAC,EAAE,OAAO,IAAI;QAC9C,MAAM8H,MAAM,GAAGD,MAAM,CAAC9M,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC;QAC/C,IAAI4M,UAAU,CAACzO,GAAG,CAAC4O,MAAM,CAAC,IAAItC,oBAAoB,CAACsC,MAAM,CAAC,EAAE;UAC1D,OAAO,KAAK;;QAEd,OAAO,IAAI;OACZ,CAAC;MAEFlB,iBAAiB,CAACgB,eAAe,EAAElB,KAAK,EAAE,KAAK,CAAC;MAChDlE,IAAI,CAACuF,MAAM,EAAE;KACd;IAEDC,WAAWA,CAACP,IAAI,EAAEf,KAAK,EAAElE,IAAI,EAAE;MAC7B,MAAMyF,QAAQ,GAAG/B,OAAO,CAACuB,IAAI,CAAC;MAC9B,IAAI,CAACQ,QAAQ,EAAE;MAEf,IAAI3F,eAAe,CAAC2F,QAAQ,CAAC1F,IAAI,EAAEC,IAAI,CAAC,EAAE;MAE1C,IAAI0F,IAAI,GAAGD,QAAQ,CAAC1F,IAAI,CAAC3U,MAAM;MAE/B,IACEqa,QAAQ,CAACP,IAAI,KAAK,QAAQ,IAC1B,QAAQ,IAAID,IAAI,IAChBA,IAAI,CAACpE,MAAM,IACXoE,IAAI,CAACU,SAAS,KAAK,WAAW,EAC9B;QACA,MAAMC,GAAG,GAAGX,IAAI,CAACpE,MAAM,CAACgB,WAAW,EAAE;QACrC6D,IAAI,GAAGA,IAAI,CAAC/K,MAAM,CAACkL,CAAC,IAClBrD,aAAa,CAAClF,IAAI,CAACmF,CAAC,IAAIA,CAAC,CAAC1E,IAAI,CAAC8H,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC/J,QAAQ,CAAC8J,GAAG,CAAC,GAAG,IACzD,CAAC;;MAGHxB,iBAAiB,CAACsB,IAAI,EAAExB,KAAK,CAAC;KAC/B;IAED4B,SAASA,CAACb,IAAI,EAAEf,KAAK,EAAElE,IAAI,EAAE;MAC3B,IAAIiF,IAAI,CAACC,IAAI,KAAK,IAAI,EAAE;QACtB,IAAID,IAAI,CAACc,GAAG,KAAK,iBAAiB,EAAE;UAClC/F,IAAI,CAACqB,WAAW,CACd1B,CAAC,CAAC4B,cAAc,CACd2C,KAAK,CAACO,mBAAmB,CACvBtC,gBAAgB,CAAC,aAAa,EAAEC,eAAe,EAAEC,GAAG,CAAC,EACrD,YACF,CAAC,EACD,CAAErC,IAAI,CAACC,IAAI,CAAwB+F,KAAK,CAAC;WAE7C,CAAC;;;QAEH;;MAGF,IAAIhG,IAAI,CAACyB,UAAU,CAACwE,iBAAiB,CAAC;QAAEC,QAAQ,EAAE;OAAU,CAAC,EAAE;MAE/D,IAAIjB,IAAI,CAACC,IAAI,KAAK,UAAU,EAAE;;QAE5B,IAAI,CAAClF,IAAI,CAACmG,kBAAkB,EAAE,EAAE;QAChC,IAAI,CAACnG,IAAI,CAACoG,YAAY,EAAE,EAAE;QAC1B,IAAIpG,IAAI,CAACyB,UAAU,CAAC4E,kBAAkB,EAAE,EAAE;QAC1C,IAAI1G,CAAC,CAAC2G,OAAO,CAACtG,IAAI,CAACC,IAAI,CAACY,MAAM,CAAC,EAAE;UAC/B;;QAGF,IAAIoE,IAAI,CAACc,GAAG,KAAK,iBAAiB,EAAE;UAClC,IAAI,CAAC/C,oBAAoB,CAAC,oBAAoB,CAAC,EAAE;UAEjD,MAAM;YAAE9C,MAAM;YAAED;WAAM,GAAGD,IAAI;UAC7B,IAAIL,CAAC,CAACQ,gBAAgB,CAACD,MAAM,EAAE;YAAEE,MAAM,EAAEH;WAAM,CAAC,EAAE;YAChD,IAAIC,MAAM,CAACG,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;cACjCN,IAAI,CAACyB,UAAU,CAACJ,WAAW,CACzB1B,CAAC,CAAC4B,cAAc,CACd2C,KAAK,CAACO,mBAAmB,CACvBtC,gBAAgB,CAAC,cAAc,EAAEC,eAAe,EAAEC,GAAG,CAAC,EACtD,aACF,CAAC,EACD,CAACpC,IAAI,CAACY,MAAM,CACd,CACF,CAAC;cACDb,IAAI,CAACuG,IAAI,EAAE;aACZ,MAAM;cACL5F,UAAU,CACRX,IAAI,EACJkE,KAAK,CAACO,mBAAmB,CACvBtC,gBAAgB,CAAC,qBAAqB,EAAEC,eAAe,EAAEC,GAAG,CAAC,EAC7D,mBACF,CACF,CAAC;;WAEJ,MAAM;YACLrC,IAAI,CAACqB,WAAW,CACd1B,CAAC,CAAC4B,cAAc,CACd2C,KAAK,CAACO,mBAAmB,CACvBtC,gBAAgB,CAAC,qBAAqB,EAAEC,eAAe,EAAEC,GAAG,CAAC,EAC7D,mBACF,CAAC,EACD,CAACrC,IAAI,CAACC,IAAI,CAACY,MAAM,CACnB,CACF,CAAC;;UAGH;;;MAIJ,IAAI4E,QAAQ,GAAG/B,OAAO,CAACuB,IAAI,CAAC;MAC5B,IAAI,CAACQ,QAAQ,EAAE;MAEf,IAAI3F,eAAe,CAAC2F,QAAQ,CAAC1F,IAAI,EAAEC,IAAI,CAAC,EAAE;MAE1C,IACEoC,eAAe,IACfqD,QAAQ,CAAC1F,IAAI,CAAC5U,IAAI,IAClBsa,QAAQ,CAAC1F,IAAI,CAAC5U,IAAI,CAACiS,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EACzC;;QAEAqI,QAAQ,GAAG;UACT,GAAGA,QAAQ;UACX1F,IAAI,EAAE;YACJ,GAAG0F,QAAQ,CAAC1F,IAAI;YAChB5U,IAAI,EAAEsa,QAAQ,CAAC1F,IAAI,CAAC5U,IAAI,CAACiS,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;SAEvC;;MAGH,IAAIqI,QAAQ,CAACP,IAAI,KAAK,QAAQ,EAAE;QAC9B,MAAMtE,EAAE,GAAG0D,eAAe,CAACmB,QAAQ,CAAC1F,IAAI,EAAE0F,QAAQ,CAACza,IAAI,EAAEkZ,KAAK,CAAC;QAC/D,IAAItD,EAAE,EAAEZ,IAAI,CAACqB,WAAW,CAACT,EAAE,CAAC;OAC7B,MAAM,IAAI6E,QAAQ,CAACP,IAAI,KAAK,QAAQ,EAAE;QACrC,MAAMtE,EAAE,GAAG0D,eAAe,CACxBmB,QAAQ,CAAC1F,IAAI,EACb0F,QAAQ,CAACza,IAAI,EACbkZ,KAAK;;QAELe,IAAI,CAACpE,MACP,CAAC;QACD,IAAID,EAAE,EAAEZ,IAAI,CAACqB,WAAW,CAACT,EAAE,CAAC;OAC7B,MAAM,IAAI6E,QAAQ,CAACP,IAAI,KAAK,UAAU,EAAE;QACvC,MAAMtE,EAAE,GAAG0D,eAAe,CACxBmB,QAAQ,CAAC1F,IAAI,EACZ,GAAE0F,QAAQ,CAACza,IAAK,kBAAiB,EAClCkZ,KAAK;;QAELe,IAAI,CAACpE,MACP,CAAC;QACD,IAAI,CAACD,EAAE,EAAE;QAET,MAAM;UAAEX;SAAM,GAAGD,IAAoC;QACrD,IAAIL,CAAC,CAACQ,gBAAgB,CAACH,IAAI,CAACE,MAAM,EAAE;UAAEE,MAAM,EAAEH;SAAM,CAAC,EAAE;UACrDU,UAAU,CAACX,IAAI,EAAEY,EAAE,CAAC;SACrB,MAAM;UACLZ,IAAI,CAACqB,WAAW,CAAC1B,CAAC,CAAC4B,cAAc,CAACX,EAAE,EAAE,CAACX,IAAI,CAACY,MAAM,CAAC,CAAC,CAAC;;;KAG1D;IAED2F,OAAO,EAAEzD,MAAM,KAAK,cAAc,IAAI;;MAEpC0D,cAAcA,CAACzG,IAAgC,EAAE;QAC/C,IAAIA,IAAI,CAAC3J,GAAG,CAAC,QAAQ,CAAC,CAACqQ,QAAQ,EAAE,EAAE;UACjC,MAAMxC,KAAK,GAAGpB,QAAQ,CAAC9C,IAAI,CAAC;UAE5B,IAAIwD,SAAS,EAAE;;YAEbY,iBAAiB,CAACjY,gCAAgC,EAAE+X,KAAK,CAAC;WAC3D,MAAM;YACLE,iBAAiB,CAAClY,mBAAmB,EAAEgY,KAAK,CAAC;;;OAGlD;;MAGD/S,QAAQA,CAAC6O,IAA0B,EAAE;QACnC,IAAIA,IAAI,CAACC,IAAI,CAAC0G,KAAK,EAAE;UACnBvC,iBAAiB,CAAClY,mBAAmB,EAAE4W,QAAQ,CAAC9C,IAAI,CAAC,CAAC;;OAEzD;;MAGD,6BAA6B4G,CAC3B5G,IAAiD,EACjD;QACAoE,iBAAiB,CAACvY,eAAe,EAAEiX,QAAQ,CAAC9C,IAAI,CAAC,CAAC;OACnD;;MAGD6G,aAAaA,CAAC7G,IAA+B,EAAE;QAC7C,IAAI,CAACA,IAAI,CAACyB,UAAU,CAACqF,kBAAkB,EAAE,EAAE;UACzC1C,iBAAiB,CAACvY,eAAe,EAAEiX,QAAQ,CAAC9C,IAAI,CAAC,CAAC;;OAErD;;MAGD+G,eAAeA,CAAC/G,IAAiC,EAAE;QACjD,IAAIA,IAAI,CAACC,IAAI,CAAC+G,QAAQ,EAAE;UACtB5C,iBAAiB,CAACvY,eAAe,EAAEiX,QAAQ,CAAC9C,IAAI,CAAC,CAAC;;OAErD;;MAGDiH,KAAKA,CAACjH,IAAuB,EAAE;QAAA,IAAAkH,qBAAA;QAC7B,MAAMC,aAAa,GACjB,EAAAD,qBAAA,GAAAlH,IAAI,CAACC,IAAI,CAACmH,UAAU,qBAApBF,qBAAA,CAAsB5G,MAAM,KAC5BN,IAAI,CAACC,IAAI,CAACoH,IAAI,CAACA,IAAI,CAAC/J,IAAI,CACtBgK,EAAE;UAAA,IAAAC,WAAA;UAAA,QAAAA,WAAA,GAAKD,EAAE,CAAmBF,UAAU,qBAAhCG,WAAA,CAAkCjH,MAAM;SAChD,CAAC;QACH,IAAI6G,aAAa,EAAE;UACjB/C,iBAAiB,CAACtX,6BAA6B,EAAEgW,QAAQ,CAAC9C,IAAI,CAAC,CAAC;;;;GAIvE;AACH,CAAC,CAAC;;;;"}