{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,MAAM,EAAW,MAAM,SAAS,CAAC;AAI1C,MAAM,UAAU,OAAO,CAAC,KAAa,EAAE,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IAC1D,OAAO,MAAM,CAAC,KAAK,aACjB,SAAS,EAAE,GAAG,IACX,OAAO,EACV,CAAC;AACL,CAAC", "sourcesContent": ["import { noCase, Options } from \"no-case\";\n\nexport { Options };\n\nexport function dotCase(input: string, options: Options = {}) {\n  return noCase(input, {\n    delimiter: \".\",\n    ...options,\n  });\n}\n"]}