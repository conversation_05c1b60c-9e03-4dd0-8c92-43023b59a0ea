{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\nimport { types as t, NodePath } from '@babel/core'\n\nconst removeJSXEmptyExpression = () => ({\n  visitor: {\n    JSXExpressionContainer(path: NodePath<t.JSXExpressionContainer>) {\n      if (t.isJSXEmptyExpression(path.get('expression'))) {\n        path.remove()\n      }\n    },\n  },\n})\n\nexport default removeJSXEmptyExpression\n"], "names": ["t"], "mappings": ";;;;AAGA,MAAM,2BAA2B,OAAO;AAAA,EACtC,OAAS,EAAA;AAAA,IACP,uBAAuB,IAA0C,EAAA;AAC/D,MAAA,IAAIA,WAAE,oBAAqB,CAAA,IAAA,CAAK,GAAI,CAAA,YAAY,CAAC,CAAG,EAAA;AAClD,QAAA,IAAA,CAAK,MAAO,EAAA,CAAA;AAAA,OACd;AAAA,KACF;AAAA,GACF;AACF,CAAA;;;;"}