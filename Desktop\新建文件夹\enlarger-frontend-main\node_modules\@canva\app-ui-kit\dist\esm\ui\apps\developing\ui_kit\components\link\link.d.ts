/** 
 * The props for the `Link` component.
 */
export type LinkProps = {
    /** 
         * The URL of an external web page.
         */
    href: string;
    /** 
         * A callback that runs when the user clicks the link.
         * To open an external web page, this callback must use the `requestOpenExternalUrl` method from the Apps SDK.
         */
    requestOpenExternalUrl: () => void;
    /** 
         * An accessible name for the link that is read aloud to screen readers.
         * This must be provided when the link does not contain text contents or when the text is not
         * sufficiently descriptive of its purpose.
         */
    ariaLabel?: string;
    /** 
         * The content to render inside the link.
         */
    children?: React.ReactNode;
    disabled?: boolean
    /** 
         * A human readable label that appears in a tooltip when the user's cursor hovers over the `Link`.
         */
    tooltipLabel?: string;
    /**  @deprecated
         * A human readable description of the link.
         * This description is used as tooltip label and aria label, if `tooltipLabel` and `ariaLabel` are not provided respectively.
         * Prefer to use `tooltipLabel` and `ariaLabel` directly instead.
         */
    title?: string;
    /** 
         * The DOM ID for the underlying `HTMLAnchorElement`.
         */
    id?: string;
};
/** 
 * Links to an external web page.
 */
export declare function Link(props: LinkProps): React.JSX.Element;
