/** 
 * The size of a progress bar.
 */
export type ProgressBarSize = 'small' | 'medium';
/** 
 * The props for the `ProgressBar` component.
 */
export type ProgressBarProps = {
    /** 
         * A callback that runs after the progress bar animation for the current value has finished.
         * For example, if the value changes from 1 to 10 and then to 100 while the change from 1 to 10 is animating, the callback is only called after the value reaches 100.
         * @param progress - The current value of the progress bar.
         */
    onProgressAnimationEnd?(progress: number): void;
    /** 
         * The size of the progress bar.
         * @defaultValue "medium"
         */
    size?: ProgressBarSize;
    /** 
         * The current value of the progress bar as an integer between 0 and 100 (inclusive).
         */
    value: number;
    /** 
         * An accessible name for the progress bar that is read aloud to screen readers.
         */
    ariaLabel?: string;
};
/** 
 * Visually represents the percentage completion of a task or operation.
 */
export declare function ProgressBar(props: ProgressBarProps): React.JSX.Element;
