import * as React from 'react';
import type { ScrollPanControls } from './scroll_pan';
export type UseTouchScrollOptions = {
    panControls: ScrollPanControls;
};
export declare function useTouchScroll({ panControls }: UseTouchScrollOptions): {
    touchScrollRef: (instance: HTMLElement | null) => void | undefined | React.DO_NOT_USE_OR_YOU_WILL_BE_FIRED_CALLBACK_REF_RETURN_VALUES[keyof React.DO_NOT_USE_OR_YOU_WILL_BE_FIRED_CALLBACK_REF_RETURN_VALUES];
};
export declare function useTouchScrollIsolation<E extends HTMLElement>(): React.Ref<E>;
