import type { PanEvent } from '../../gestures/gesture_recognizer';
import { PanRecognizer } from '../../gestures/gesture_recognizer';
export type SheetState = 'collapsed' | 'expanded' | 'peek';
export interface SheetBehaviorController {
    setInitialOpenSheetState: (state: SheetState) => void
    setEnableDragging: (enable: boolean) => void
    animateToSheetState: (state: SheetState) => void
    resetToPreviousSheetState: () => void
    setContainerHeightToContentHeight: (contentHeight?: number) => void
}
export declare class BottomSheetDragBehavior implements SheetBehaviorController {
    private readonly _window;
    private readonly onStateChanged?;
    readonly peekSizePx: number | undefined;
    private readonly transparentOverlayOnPeek;
    private readonly onPanning?;
    private readonly calculateNextState;
    private sheetState;
    private _hasTransition;
    private _containerSize;
    private _viewPortSize;
    private _isPanning;
    private _isSwiping;
    get containerTransition(): string;
    get containerMaxSizeInPeek(): string;
    get isSwiping(): boolean;
    get containerTransform(): string;
    get transparentOverlay(): boolean;
    private _initialOpenSheetState;
    private previousStaticSheetState;
    private enableDragging;
    private minSize;
    private midSize;
    private maxSize;
    private contentHeight?;
    private readonly viewportTopOffset;
    private get hasPeekState();
    constructor(_window: Pick<Window, 'innerHeight'>, onStateChanged?: ((state: SheetState) => void) | undefined, peekSizePx?: number | undefined, transparentOverlayOnPeek?: boolean, offsetForViewportHeight?: number, onPanning?: ((isPanning: boolean) => void) | undefined, calculateNextState?: typeof calculateNextStateImpl);
    private currentOpenState;
    readonly recognizer: PanRecognizer;
    setInitialOpenSheetState(state: SheetState): void;
    setEnableDragging(enable: boolean): void;
    resetToPreviousSheetState(): void;
    setContainerHeightToContentHeight(contentHeight?: number): void;
    configureBounds(bound: {
        height?: number;
    }, open: boolean): void;
    closeSheet(): void;
    private onEventStart;
    private onMoveVertical;
    private onEndVertical;
    animateToSheetState(state: SheetState): void;
    onViewportHeightChanges(viewportHeight: number): void;
    private updateSheetState;
    private get snapSize();
    private clampBetweenMinAndMaxSize;
}
export declare function isVerticalDirection(e: BottomSheetDragPanEvent): boolean;
export declare function calculateNextStateImpl(
 { direction, deltaSize, velocity, hasPeekState, midSize, maxSize, }: {
     direction: 'up' | 'down' | 'left' | 'right'
     deltaSize: number
     velocity: number
     hasPeekState: boolean;
     midSize: number;
     maxSize: number;
 }
): SheetState;
export type BottomSheetDragPanEvent = Pick<PanEvent, 'direction' | 'distanceY' | 'velocityY'>;
