import * as React from 'react';
import { Breakpoint } from '../metrics/metrics';
export type PixelDensitySource = {
    src: string;
    pixelDensity: number;
};
type IntrinsicWidthSource = {
    src: string
    intrinsicWidth: number
};
type BreakpointSource = {
    images: IntrinsicWidthSource[];
    breakpoint?: Breakpoint;
};
type Length = {
    type: 'px' | 'vw';
    value: number;
};
type Size = {
    breakpoint?: Length;
    width: Length;
};
type ResponsiveImageProps = Omit<React.JSX.IntrinsicElements['img'], 'srcSet' | 'sizes'> & {
    alt: string
};
type PixelDensityVariantProps = ResponsiveImageProps & {
    sources: PixelDensitySource[];
};
export type ViewportVariantProps = ResponsiveImageProps & {
    sources: IntrinsicWidthSource[]
    sizes: Size[]
};
type PictureViewportProps = Omit<React.JSX.IntrinsicElements['img'], 'srcSet' | 'sizes'> & {
    alt: string;
    sources: BreakpointSource[];
    src?: string;
};
export declare const PixelDensityResponsiveImage: React.ForwardRefExoticComponent<Omit<PixelDensityVariantProps, "ref"> & React.RefAttributes<HTMLImageElement>>;
export declare const ViewportResponsiveImage: React.ForwardRefExoticComponent<Omit<ViewportVariantProps, "ref"> & React.RefAttributes<HTMLImageElement>>;
export declare const ViewportResponsivePicture: React.ForwardRefExoticComponent<Omit<PictureViewportProps, "ref"> & React.RefAttributes<HTMLImageElement>>;
export {};
