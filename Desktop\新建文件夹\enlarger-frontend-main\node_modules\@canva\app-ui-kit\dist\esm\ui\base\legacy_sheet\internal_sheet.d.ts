import * as React from 'react';
import type * as ReactDOM from 'react-dom';
import type { PanRecognizer } from '../gestures/gesture_recognizer';
import type { EaselConfiguration } from '../provider/provider';
import type { Layers, Sheet } from './internal/layers';
import type { BottomSheetDragBehavior } from './internal/sheet_behavior';
import type { LegacySheetProps, SheetDraggable, Side } from './legacy_sheet';
type BaseInternalSheetProps = LegacySheetProps & {
    layers: Layers;
    createPortal: typeof ReactDOM.createPortal;
    sheetBehavior?: BottomSheetDragBehavior;
    windowOverride?: Window;
    draggable?: SheetDraggable;
    hasShadow?: boolean;
    easelConfiguration: EaselConfiguration;
};
export type InternalSheetProps = BaseInternalSheetProps & ({
    enableLayerDraggable: false;
} | {
    enableLayerDraggable: true;
    maxVisibleSizePx?: number
    onLayerDraggingThresholdTriggered?: (opts: {
        direction: 'opening' | 'closing';
    }) => void
});
type SheetDragInternal = {
    sheetBehavior?: BottomSheetDragBehavior;
    recognizers?: PanRecognizer[];
    createHandle?: boolean;
};
export declare const InternalSheet: React.ForwardRefExoticComponent<InternalSheetProps & React.RefAttributes<InternalSheetClass>>;
declare class InternalSheetClass extends React.Component<InternalSheetProps & {
    interactionLayerRef: (element: HTMLElement | null) => void;
}> implements Sheet {
    static readonly defaultProps: Pick<InternalSheetProps, 'scrollable'>;
    private static ids;
    readonly id: string;
    contentElement: HTMLDivElement | undefined;
    outerElement: HTMLDivElement | undefined;
    private closeTimeout;
    readonly sheetDragInternal?: SheetDragInternal;
    parentLayerContainer?: HTMLDivElement;
    easelConfiguration: EaselConfiguration;
    show: boolean;
    private currentContent?;
    private currentOuterContent?;
    private themeData?;
    get themeClass(): string | undefined;
    window: Window | undefined;
    get supportsSafeAreaInset(): boolean | undefined;
    constructor(props: InternalSheetProps & {
        interactionLayerRef: (element: HTMLElement | null) => void;
    });
    render(): import("react/jsx-runtime").JSX.Element;
    private setThemeData;
    private renderPortal;
    componentDidMount(): void;
    componentWillUnmount(): void;
    componentDidUpdate(prevProps: LegacySheetProps): void;
    private contentShouldUpdate;
    private get unmountOnClose();
    get applyInsetStyles(): boolean;
    get from(): Side;
    get onBottom(): boolean;
    get roundedCorners(): boolean;
    get autoFocusOnOpen(): boolean;
    get enableAnimations(): boolean;
    get isFullscreen(): boolean;
    get contentStyles(): React.CSSProperties;
    private readonly onBackButton;
}
export {};
