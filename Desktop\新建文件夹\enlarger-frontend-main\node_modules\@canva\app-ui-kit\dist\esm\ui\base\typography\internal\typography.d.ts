import * as React from 'react';
import type { BasicHTMLHandle } from '../../handle/handle';
import type { TextAriaProps, TitleAriaProps } from './typography_aria_attributes';
type GenericTagName = keyof Pick<React.JSX.IntrinsicElements, 'div' | 'span' | 'li' | 'blockquote'>;
type HeadingTagName = keyof Pick<React.JSX.IntrinsicElements, 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'> | GenericTagName;
type ParagraphTagName = keyof Pick<React.JSX.IntrinsicElements, 'p'> | GenericTagName;
export declare const InheritColor: unique symbol;
export declare const typographyTones: readonly ["primary", "secondary", "tertiary", "critical", "info", "success", "warn", typeof InheritColor];
export type TypographyTone = (typeof typographyTones)[number];
export type TypographyVariant = TypographyWeight | 'title';
export type TypographyWeight = 'regular' | 'bold';
export type TypographyAlignment = 'center' | 'end' | 'inherit' | 'start';
export declare const typographySizes: readonly ["xxlarge", "xlarge", "large", "medium", "small", "xsmall", "xxsmall"];
export type TypographySize = (typeof typographySizes)[number];
export type TextTypographySize = Exclude<TypographySize, 'xxsmall'>;
export type TitleTypographySize = TypographySize;
export type TypographyHandle = BasicHTMLHandle;
type SharedTypographyProps = {
    id?: string
    alignment?: TypographyAlignment
    tabIndex?: -1
    tone?: TypographyTone
    margins?: 'none' | 'legacy'
    lineClamp?: number
    wrapStyle?: 'unset' | 'balance' | 'pretty'
    allowUserSelect?: boolean
    capitalization?: 'default' | 'uppercase'
    className?: string
    children?: React.ReactNode
    elementTiming?: string
};
export type TextProps = SharedTypographyProps & TextAriaProps & {
    weight?: TypographyWeight
    size?: TextTypographySize
    tagName?: ParagraphTagName
};
export type TitleProps = SharedTypographyProps & TitleAriaProps & {
    size?: TitleTypographySize
    tagName?: HeadingTagName
};
export declare const Text: React.MemoExoticComponent<React.ForwardRefExoticComponent<SharedTypographyProps & TextAriaProps & {
    weight?: TypographyWeight
    size?: TextTypographySize
    tagName?: ParagraphTagName
} & React.RefAttributes<BasicHTMLHandle>>>;
export declare const Title: React.MemoExoticComponent<React.ForwardRefExoticComponent<SharedTypographyProps & {
    ariaHidden?: boolean;
    ariaLabel?: string;
    ariaLive?: "assertive" | "off" | "polite";
} & {
    size?: TitleTypographySize
    tagName?: HeadingTagName
} & React.RefAttributes<BasicHTMLHandle>>>;
export declare function getDefaultHeadingTagName(size?: TypographySize): "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
export {};
