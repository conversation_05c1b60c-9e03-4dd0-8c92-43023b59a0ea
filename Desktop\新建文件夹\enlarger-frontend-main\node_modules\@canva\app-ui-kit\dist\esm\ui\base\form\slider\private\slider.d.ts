import * as React from 'react';
import type { NumberInputProps } from '../../number_input/number_input';
import type { TypographySize } from '../../../typography/typography';
import type { SharedSliderProps } from './base_slider';
export type SliderProps = SharedSliderProps & {
    value?: number
    defaultValue?: number
    NumericInput?: React.ComponentType<SliderNumericInputProps> | 'none'
    autoFocusNumericInput?: 'on-desktop' | 'always'
    blurOnDragEnd?: boolean
    label?: string
    labelSize?: Extract<TypographySize, 'small' | 'medium'>
    onDragEnd?(): void
    onChange?: (value: number) => void
};
export declare const Slider: React.FunctionComponent<SliderProps>;
export type SliderNumericInputProps = Pick<NumberInputProps, 'className' | 'inputClassName' | 'min' | 'max' | 'step' | 'disabled' | 'autoFocus' | 'onChange' | 'ariaLabel' | 'ariaLabelledBy' | 'ariaDescribedBy' | 'placeholder' | 'onBlur' | 'onFocus' | 'onKeyDown'> & {
    value?: string;
    onChangeComplete: (value: number) => void
};
