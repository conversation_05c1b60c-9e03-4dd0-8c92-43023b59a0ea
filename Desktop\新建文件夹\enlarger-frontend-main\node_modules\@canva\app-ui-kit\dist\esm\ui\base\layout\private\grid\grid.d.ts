import * as React from 'react';
import type { CommonProps } from '../../../box/common_props/common_props';
import type { Space } from '../../../metrics/metrics';
import type { RequiredResponsiveValue, ResponsiveValue } from '../../../responsive/responsive';
export declare const columns: readonly [1, 2, 3, 4, 5, 6, 7];
export type GridColumn = (typeof columns)[number];
export declare const rowSizes: readonly ["auto", "equal"];
export type GridRowSize = (typeof rowSizes)[number];
export declare const aligns: readonly ["stretch", "start", "center", "end"];
export type GridAlign = (typeof aligns)[number];
export type GridProps = {
    columns: RequiredResponsiveValue<GridColumn>
    rowSize?: ResponsiveValue<GridRowSize>
    spacing?: ResponsiveValue<Space>
    spacingX?: ResponsiveValue<Space>
    spacingY?: ResponsiveValue<Space>
    alignY?: ResponsiveValue<GridAlign>
    alignX?: ResponsiveValue<GridAlign>
} & CommonProps;
export declare const Grid: React.ForwardRefExoticComponent<{
    columns: RequiredResponsiveValue<GridColumn>
    rowSize?: ResponsiveValue<GridRowSize>
    spacing?: ResponsiveValue<Space>
    spacingX?: ResponsiveValue<Space>
    spacingY?: ResponsiveValue<Space>
    alignY?: ResponsiveValue<GridAlign>
    alignX?: ResponsiveValue<GridAlign>
} & CommonProps & {
    children?: React.ReactNode | undefined;
} & React.RefAttributes<HTMLElement>>;
