import * as React from 'react';
import type { ScrollPanControls } from './scroll_pan';
export type UseWheelScrollOptions = {
    panControls: ScrollPanControls;
};
export declare function useWheelScroll({ panControls }: UseWheelScrollOptions): {
    wheelRef: (instance: HTMLElement | null) => void | undefined | React.DO_NOT_USE_OR_YOU_WILL_BE_FIRED_CALLBACK_REF_RETURN_VALUES[keyof React.DO_NOT_USE_OR_YOU_WILL_BE_FIRED_CALLBACK_REF_RETURN_VALUES];
};
export declare function useWheelScrollIsolation<E extends HTMLElement>(): React.Ref<E>;
