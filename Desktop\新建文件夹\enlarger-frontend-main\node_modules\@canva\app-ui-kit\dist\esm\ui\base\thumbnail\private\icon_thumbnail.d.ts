import type { Icon, IconSize, IconTone } from '../../icons/icons';
import type { SizingProps } from './thumbnail_container';
export type IconThumbnailProps = SizingProps & {
    Icon: Icon
    size?: IconSize
    tone?: IconTone
    background?: 'none' | 'secondary'
    border?: 'none' | 'low'
    padding?: 'none' | '1u' | '2u'
    borderRadius?: 'none' | 'elementSmall' | 'element' | 'elementRelaxed'
};
export declare function IconThumbnail(
 { tone, Icon, size, background, border, padding, borderRadius, aspectRatio, width, height, }: IconThumbnailProps
): import("react/jsx-runtime").JSX.Element;
