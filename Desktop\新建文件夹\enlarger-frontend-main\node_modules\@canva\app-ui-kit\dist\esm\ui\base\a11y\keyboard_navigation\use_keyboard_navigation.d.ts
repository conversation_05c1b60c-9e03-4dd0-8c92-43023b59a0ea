type KeyboardEventHandler = (e: KeyboardEvent, index: number) => void;
export type KeyboardNavigationOptions = {
    direction?: 'vertical' | 'horizontal'
    mode?: 'default' | 'cycle'
    handleInputs?: boolean
    extraNavigation?: boolean
    onChange?(index: number): void
    onPrev?: KeyboardEventHandler
    onNext?: KeyboardEventHandler
    onFirst?: KeyboardEventHandler
    onLast?: KeyboardEventHandler
    onSelect?: KeyboardEventHandler
    onExit?: KeyboardEventHandler
};
export type KeyboardNavigation = {
    ref: (arg: HTMLElement | null) => void
    index: number
    moveTo: (index?: number) => void
    moveToFirst: () => void
    moveToLast: () => void
};
export declare function useKeyboardNavigation(size: number, options?: KeyboardNavigationOptions): KeyboardNavigation;
export {};
