import * as React from 'react';
import type { ButtonHandle } from '../../button/button';
import type { TooltipProps } from '../../tooltip/tooltip';
export type PillRole = 'button' | 'switch' | 'combobox';
export type PillMaxWidth = '25u' | '100%';
export type PillHandle = ButtonHandle;
export type PillProps = {
    text: string
    size?: 'tiny' | 'small' | 'medium'
    shape?: 'round' | 'rectangle'
    active?: boolean
    selected?: boolean
    pressed?: boolean
    disabled?: boolean
    maxWidth?: PillMaxWidth
    ref?: React.RefObject<PillHandle | null>
    start?: React.ReactNode | (() => React.ReactNode)
    end?: React.ReactNode | (() => React.ReactNode)
    id?: string
} & PillEventHandlerProps & PillA11yProps & PillTooltipProps;
type PillEventHandlerProps = {
    onRemoveClick?(): void
    onClick?(): void
};
type PillA11yProps = {
    role?: PillRole
    disclosure?: boolean
    ariaLabel?: string
    ariaLabelledBy?: string
    ariaDescribedBy?: string
    ariaControls?: string
    ariaHasPopup?: 'listbox'
    ariaActiveDescendant?: string
};
type PillTooltipProps = {
    tooltipLabel?: TooltipProps['label']
    tooltipDescription?: TooltipProps['description']
    tooltipDisabled?: TooltipProps['disabled']
    tooltipPlacement?: TooltipProps['placement']
    tooltipLineClamp?: TooltipProps['lineClamp']
};
export declare const Pill: React.ForwardRefExoticComponent<Omit<PillProps, "ref"> & React.RefAttributes<import("../../handle/handle").BasicHTMLHandle>>;
export {};
