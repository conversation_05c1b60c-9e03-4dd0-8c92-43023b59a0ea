import * as React from 'react';
import type { ButtonProps } from '../../button/button';
import type { FocusHandle } from '../../handle/handle';
import type { Shape, Size } from './internal_swatch';
import type { Fill, FillObject as InternalFillObject, GradientStop as InternalGradientStop } from './swatch_util';
export type FillObject = InternalFillObject;
export type GradientStop = InternalGradientStop;
export type SwatchHandle = FocusHandle;
type FluidProps = (Pick<ButtonProps, 'stretch'> & {
    stretch: true
    aspectRatio?: number
}) | {
    stretch?: false;
    aspectRatio?: never;
};
export type SwatchProps = Pick<ButtonProps, 'onClick' | 'active' | 'selected' | 'pressed' | 'disclosure' | 'id' | 'ariaLabelledBy' | 'ariaDescribedBy' | 'disabled' | 'tabIndex' | 'ref' | 'role' | 'ariaHasPopup' | 'ariaControls'> & {
    fill: Fill
    variant?: 'solid' | 'outline' | 'contrast'
    size?: Size
    shape?: Shape
    Icon?: () => React.ReactNode
    onDelete?: () => void
    deleteButtonVisibility?: 'on-hover' | 'always'
    tooltipLabel?: string
    tooltipDescription?: string
    ariaLabel?: string
    disableTooltip?: boolean
    colorNames?: ReadonlyMap<string, string>
    rgbToCmyk?: ReadonlyMap<string, string>
} & FluidProps;
export declare const Swatch: React.ForwardRefExoticComponent<(Omit<Pick<ButtonProps, "ref" | "active" | "disabled" | "role" | "id" | "ariaLabelledBy" | "ariaDescribedBy" | "tabIndex" | "onClick" | "pressed" | "selected" | "ariaHasPopup" | "disclosure" | "ariaControls"> & {
    fill: Fill
    variant?: "solid" | "outline" | "contrast"
    size?: Size
    shape?: Shape
    Icon?: () => React.ReactNode
    onDelete?: () => void
    deleteButtonVisibility?: "on-hover" | "always"
    tooltipLabel?: string
    tooltipDescription?: string
    ariaLabel?: string
    disableTooltip?: boolean
    colorNames?: ReadonlyMap<string, string>
    rgbToCmyk?: ReadonlyMap<string, string>
} & Pick<ButtonProps, "stretch"> & {
    stretch: true
    aspectRatio?: number
}, "ref"> | Omit<Pick<ButtonProps, "ref" | "active" | "disabled" | "role" | "id" | "ariaLabelledBy" | "ariaDescribedBy" | "tabIndex" | "onClick" | "pressed" | "selected" | "ariaHasPopup" | "disclosure" | "ariaControls"> & {
    fill: Fill
    variant?: "solid" | "outline" | "contrast"
    size?: Size
    shape?: Shape
    Icon?: () => React.ReactNode
    onDelete?: () => void
    deleteButtonVisibility?: "on-hover" | "always"
    tooltipLabel?: string
    tooltipDescription?: string
    ariaLabel?: string
    disableTooltip?: boolean
    colorNames?: ReadonlyMap<string, string>
    rgbToCmyk?: ReadonlyMap<string, string>
} & {
    stretch?: false;
    aspectRatio?: never;
}, "ref">) & React.RefAttributes<FocusHandle>>;
export type AddColorButtonProps = Omit<SwatchProps, 'Icon' | 'fill' | 'variant' | 'onDelete'> & FluidProps;
export declare const AddColorButton: (props: AddColorButtonProps) => import("react/jsx-runtime").JSX.Element;
export {};
