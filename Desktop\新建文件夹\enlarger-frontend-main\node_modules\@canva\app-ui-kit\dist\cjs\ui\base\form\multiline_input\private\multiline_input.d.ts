import * as React from 'react';
export type MultilineInputProps = {
    autoGrow?: boolean
    maxRows?: number
    minRows?: number
    bufferRows?: number
    disabled?: boolean
    required?: boolean
    readOnly?: boolean
    error?: boolean
    borderless?: boolean
    placeholder?: string
    resize?: boolean
    lang?: string
    value?: string
    maxLength?: number
    autoComplete?: string
    dir?: 'auto' | 'rtl' | 'ltr'
    id?: string
    footer?: React.ReactNode
    className?: string
    inputClassName?: string
} & MultilineInputA11yProps & MultilineInputEventHandlerProps;
type MultilineInputA11yProps = {
    ariaLabel?: string
    ariaLabelledBy?: string
    ariaDescribedBy?: string
};
type MultilineInputEventHandlerProps = {
    onClick?: React.MouseEventHandler
    onChange?: (value: string, event?: React.ChangeEvent<HTMLTextAreaElement>) => void
    onChangeComplete?: (value: string) => void
    onKeyPress?: (event: React.KeyboardEvent<HTMLTextAreaElement>) => void
    onKeyDown?: (event: React.KeyboardEvent<HTMLTextAreaElement>) => void
    onKeyUp?: (event: React.KeyboardEvent<HTMLTextAreaElement>) => void
    onFocus?: React.FocusEventHandler
    onBlur?: React.FocusEventHandler
    onPaste?: (e: React.ClipboardEvent<HTMLTextAreaElement>) => void
};
export declare const MultilineInput: React.MemoExoticComponent<React.ForwardRefExoticComponent<Omit<{
    autoGrow?: boolean
    maxRows?: number
    minRows?: number
    bufferRows?: number
    disabled?: boolean
    required?: boolean
    readOnly?: boolean
    error?: boolean
    borderless?: boolean
    placeholder?: string
    resize?: boolean
    lang?: string
    value?: string
    maxLength?: number
    autoComplete?: string
    dir?: "auto" | "rtl" | "ltr"
    id?: string
    footer?: React.ReactNode
    className?: string
    inputClassName?: string
} & MultilineInputA11yProps & MultilineInputEventHandlerProps & React.RefAttributes<HTMLTextAreaElement>, "ref"> & React.RefAttributes<HTMLTextAreaElement>>>;
export {};
