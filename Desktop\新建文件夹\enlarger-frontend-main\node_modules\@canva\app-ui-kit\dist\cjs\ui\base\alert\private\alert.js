"use strict"
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get Alert () {
        return Alert;
    },
    get AlertIcon () {
        return AlertIcon;
    },
    get alertTones () {
        return alertTones;
    }
});
const _jsxruntime = require("react/jsx-runtime");
const _preconditions = require('../../../../base/preconditions');
const _classnames = _interop_require_default(require("classnames"));
require("react");
const _box = require('../../box/box');
const _button = require('../../button/button');
const _focusable = require('../../focusable/focusable');
const _icon = require('../../icons/alert_triangle/icon');
const _icon1 = require('../../icons/check/icon');
const _icon2 = require('../../icons/info/icon');
const _icon3 = require('../../icons/x/icon');
const _layout = require('../../layout/layout');
const _linkcss = require('../../link/internal/link.css');
const _themecss = _interop_require_default(require('../../theme/theme.css'));
const _typography = require('../../typography/typography');
const _alertcss = _interop_require_default(require("./alert.css"));
const _alertmessages = require("./alert.messages");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
const alertTones = [
    'positive',
    'warn',
    'critical',
    'info',
    'neutral'
];
const typographyTones = {
    positive: _themecss.default.colorPositiveForeLow,
    warn: _themecss.default.colorWarnForeLow,
    critical: _themecss.default.colorCriticalForeLow,
    info: _themecss.default.colorInfoForeLow,
    neutral: _themecss.default.colorNeutralForeLow
};
const AlertIcon = ({ tone, size = 'medium' })=>{
    switch(tone){
        case 'critical':
        case 'warn':
            return (0, _jsxruntime.jsx)(_icon.AlertTriangleIcon, {
                size: size
            });
        case 'positive':
            return (0, _jsxruntime.jsx)(_icon1.CheckIcon, {
                size: size
            });
        case 'info':
            return (0, _jsxruntime.jsx)(_icon2.InfoIcon, {
                size: size
            });
        case 'neutral':
            return null;
        default:
            throw new _preconditions.UnreachableError(tone);
    }
};
const Alert = ({ children, className, title, tone, dismissible, onDismiss, size = 'medium', rounded = true, showIcon = size !== 'small', Icon, focusRef })=>{
    let iconElement;
    if (showIcon) {
        if (Icon) iconElement = (0, _jsxruntime.jsx)(Icon, {
            size: size
        });
        else iconElement = (0, _jsxruntime.jsx)(AlertIcon, {
            tone: tone,
            size: size
        });
    }
    const containerRef = (0, _focusable.useFocusableRef)(focusRef);
    return (0, _jsxruntime.jsx)(_box.Box, {
        background: `${tone}Low`,
        borderRadius: rounded ? 'element' : 'none',
        paddingX: size === 'medium' ? '2u' : '1.5u',
        paddingY: size === 'medium' ? '2u' : '1u',
        width: "full",
        className: (0, _classnames.default)(className, _alertcss.default.alert, {
            [_alertcss.default.small]: size === 'small'
        }),
        customProperties: {
            [_linkcss.customProperties.linkColor]: typographyTones[tone]
        },
        role: tone === 'critical' ? 'alert' : undefined,
        ref: containerRef,
        tabIndex: focusRef != null ? -1 : undefined,
        children: (0, _jsxruntime.jsx)(_typography.Text, {
            tagName: "div",
            size: size,
            tone: _typography.InheritColor,
            children: (0, _jsxruntime.jsxs)(_layout.Columns, {
                spacing: "1u",
                alignY: "baseline",
                children: [
                    iconElement && (0, _jsxruntime.jsx)(_layout.Column, {
                        width: "content",
                        children: iconElement
                    }),
                    (0, _jsxruntime.jsxs)(_layout.Column, {
                        children: [
                            title && (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {
                                children: [
                                    (0, _jsxruntime.jsx)("strong", {
                                        children: title
                                    }),
                                    ' '
                                ]
                            }),
                            children
                        ]
                    }),
                    dismissible && (0, _jsxruntime.jsx)(_layout.Column, {
                        width: "content",
                        children: (0, _jsxruntime.jsx)(_button.Button, {
                            variant: "tertiary",
                            className: _alertcss.default.close,
                            onClick: onDismiss,
                            icon: _icon3.XIcon,
                            ariaLabel: _alertmessages.AlertMessages.dismiss(),
                            size: "small",
                            iconSize: size
                        })
                    })
                ]
            })
        })
    });
};
