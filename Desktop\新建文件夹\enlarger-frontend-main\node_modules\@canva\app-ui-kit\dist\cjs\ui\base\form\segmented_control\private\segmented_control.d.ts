import * as React from 'react';
import type { Focusable } from '../../../focusable/focusable';
export type SegmentedControlProps<T> = {
    value?: T
    defaultValue?: T
    options: SegmentedControlOption<T>[]
    disabled?: boolean
    borderless?: boolean
    id?: string
    focusRef?: React.RefObject<Focusable | null>
} & SegmentedControlA11yProps & SegmentedControlEventHandlerProps<T>;
type SegmentedControlA11yProps = {
    ariaLabel?: string
    ariaLabelledBy?: string
    ariaDescribedBy?: string
};
type SegmentedControlEventHandlerProps<T> = {
    onChange?: (value: T) => void
    onFocus?: React.FocusEventHandler
    onBlur?: React.FocusEventHandler
};
export type SegmentedControlOption<T> = {
    label: React.ReactNode
    ariaLabel?: string
    value: T
    disabled?: boolean
};
export declare function SegmentedControl<T>(props: SegmentedControlProps<T>): import("react/jsx-runtime").JSX.Element;
export {};
