import type { EasingFunction } from '../animation/easings';
export declare class ScrollPositioner {
    static animate(
     getScrollTop: () => number,
     setScrollTop: (scrollTop: number) => void,
     newScrollTop: number,
     duration?: number,
     easingFn?: EasingFunction
    ): void;
    static animateElement(
     element: HTMLElement,
     newScrollTop: number,
     duration?: number,
     easingFn?: EasingFunction
    ): void;
    static isAboveVisibleScroll(container: HTMLElement, element: HTMLElement): boolean;
    static isBelowVisibleScroll(container: HTMLElement, element: HTMLElement): boolean;
    static scrollToPutAtTop(container: HTMLElement, element: HTMLElement, withAnimation?: boolean): void;
    static scrollToPutAtBottom(container: HTMLElement, element: HTMLElement, withAnimation?: boolean): void;
    static scrollToCenter(container: HTMLElement, element: HTMLElement, withAnimation?: boolean): void;
}
