import * as React from 'react';
import type { FocusHandle } from '../../handle/handle';
import type { SwatchProps } from './swatch';
export type Shape = 'circle' | 'square';
export type Size = 'xxsmall' | 'xsmall' | 'small' | 'medium' | 'large';
export type StaticSwatchHandle = FocusHandle;
export type ClickableSwatchHandle = FocusHandle;
export type DeletableSwatchHandle = FocusHandle;
export declare function getBorderRadiusStyles(shape: Shape, size: Size): import("../../html/encode").HtmlEncodedCssList | undefined;
type StaticSwatchProps = {
    className: string;
    children: React.ReactNode;
    ariaLabel?: string;
    ariaDescribedBy?: string;
    ariaLabelledBy?: string;
    id?: string;
    isRoot: boolean;
};
export declare const StaticSwatch: React.ForwardRefExoticComponent<StaticSwatchProps & React.RefAttributes<FocusHandle>>;
type ClickableSwatchProps = Pick<SwatchProps, 'size' | 'shape' | 'onClick' | 'active' | 'selected' | 'pressed' | 'disabled' | 'disclosure' | 'role' | 'ref' | 'tabIndex' | 'ariaLabel' | 'stretch' | 'aspectRatio' | 'ariaHasPopup' | 'ariaControls' | 'tooltipLabel' | 'tooltipDescription' | 'disableTooltip'> & {
    children: React.ReactNode;
    isRoot: boolean;
};
export declare const ClickableSwatch: React.MemoExoticComponent<React.ForwardRefExoticComponent<Omit<Omit<ClickableSwatchProps, "ref"> & React.RefAttributes<FocusHandle>, "ref"> & React.RefAttributes<FocusHandle>>>;
export declare const DeletableSwatch: React.MemoExoticComponent<React.ForwardRefExoticComponent<Omit<Pick<SwatchProps, "stretch" | "aspectRatio" | "disableTooltip" | "onDelete" | "deleteButtonVisibility"> & {
    children: React.ReactNode;
    isRoot: boolean;
} & React.RefAttributes<FocusHandle>, "ref"> & React.RefAttributes<FocusHandle>>>;
export {};
