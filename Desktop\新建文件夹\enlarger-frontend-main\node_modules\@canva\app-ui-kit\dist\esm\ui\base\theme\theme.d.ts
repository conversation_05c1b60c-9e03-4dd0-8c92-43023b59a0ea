import * as React from 'react';
import { ThemeData } from './internal';
export type Appearance = 'classicLight' | 'classicDark' | 'light' | 'dark';
declare const ADAPTIVE_APPEARANCES: readonly ["classicAdaptive", "adaptive"];
export type AdaptiveAppearance = (typeof ADAPTIVE_APPEARANCES)[number];
export type AdaptiveOrStaticAppearance = Appearance | AdaptiveAppearance;
export type Theme = 'light' | 'dark';
export type BackgroundLevel = 'canvas' | 'tabdock' | 'page' | 'surface';
export type { ThemeData };
export declare function cleanThemePreload(): void;
export type ApplyThemeRootProps = {
 appearance: AdaptiveOrStaticAppearance;
}
 & Record<Appearance, Theme>;
export declare function ApplyThemeRoot({ appearance: givenAppearance, ...ccTheme }: ApplyThemeRootProps): null;
export type ThemeBoundaryProps = {
 children: (data: ThemeData) => React.ReactElement;
}
 & Partial<Record<Appearance, Theme>>;
export declare const ThemeBoundary: React.FunctionComponent<ThemeBoundaryProps>;
export declare const useThemeData: () => ThemeData;
export type WithThemeDataProps = {
    children: (data: ThemeData) => React.ReactElement | null;
};
export declare const WithThemeData: React.FunctionComponent<WithThemeDataProps>;
