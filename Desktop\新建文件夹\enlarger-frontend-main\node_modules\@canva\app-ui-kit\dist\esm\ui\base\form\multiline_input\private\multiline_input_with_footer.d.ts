import * as React from 'react';
import type { MultilineInputProps } from './multiline_input';
export type MultilineInputWithFooterProps = MultilineInputProps & {
    EndContent?: React.ReactNode
    StartContent?: React.ReactNode
};
export declare const MultilineInputWithFooter: React.ForwardRefExoticComponent<{
    autoGrow?: boolean;
    maxRows?: number;
    minRows?: number;
    bufferRows?: number;
    disabled?: boolean;
    required?: boolean;
    readOnly?: boolean;
    error?: boolean;
    borderless?: boolean;
    placeholder?: string;
    resize?: boolean;
    lang?: string;
    value?: string;
    maxLength?: number;
    autoComplete?: string;
    dir?: "auto" | "rtl" | "ltr";
    id?: string;
    footer?: React.ReactNode;
    className?: string;
    inputClassName?: string;
} & {
    ariaLabel?: string;
    ariaLabelledBy?: string;
    ariaDescribedBy?: string;
} & {
    onClick?: React.MouseEventHandler;
    onChange?: (value: string, event?: React.ChangeEvent<HTMLTextAreaElement>) => void;
    onChangeComplete?: (value: string) => void;
    onKeyPress?: (event: React.KeyboardEvent<HTMLTextAreaElement>) => void;
    onKeyDown?: (event: React.KeyboardEvent<HTMLTextAreaElement>) => void;
    onKeyUp?: (event: React.KeyboardEvent<HTMLTextAreaElement>) => void;
    onFocus?: React.FocusEventHandler;
    onBlur?: React.FocusEventHandler;
    onPaste?: (e: React.ClipboardEvent<HTMLTextAreaElement>) => void;
} & {
    EndContent?: React.ReactNode
    StartContent?: React.ReactNode
} & React.RefAttributes<HTMLTextAreaElement>>;
