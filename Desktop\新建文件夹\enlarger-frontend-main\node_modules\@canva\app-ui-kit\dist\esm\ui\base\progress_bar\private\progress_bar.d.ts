export declare const TRANSITION_END_TIMEOUT_MS: number;
export type Size = 'small' | 'medium';
export type Tone = 'info' | 'critical';
export type ProgressBarProps = {
    ariaLabel?: string
    disableBubbles?: boolean
    value: number
    onProgressAnimationEnd?(progress: number): void
    size?: Size
    disableAnimations?: boolean
    tone?: Tone
    stretch?: boolean
};
export declare const ProgressBar: (props: ProgressBarProps) => import("react/jsx-runtime").JSX.Element;
