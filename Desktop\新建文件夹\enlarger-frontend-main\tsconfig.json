{"compilerOptions": {"jsx": "react-jsx", "lib": ["dom", "dom.iterable", "es2018", "es2019.array", "es2019.object", "es2019.string", "es2020.promise", "es2020.string"], "types": ["node", "jest"], "composite": true, "declaration": true, "declarationMap": true, "experimentalDecorators": true, "importHelpers": true, "noImplicitOverride": true, "moduleResolution": "node", "rootDir": ".", "outDir": "dist", "strict": true, "skipLibCheck": true, "target": "ES2019", "sourceMap": true, "inlineSources": true, "module": "ESNext", "noImplicitAny": false, "removeComments": true, "preserveConstEnums": true, "allowSyntheticDefaultImports": true, "baseUrl": "./", "paths": {"assets": ["./assets"], "styles": ["./styles"], "@canva/preview/*": ["./sdk/preview/*"]}}, "include": ["./src/**/*", "./scripts/**/*", "./examples/**/*", "./declarations/declarations.d.ts", "./utils/**/*", "./styles/**/*", "./node_modules/@types/**/*", "./sdk/**/*"], "ts-node": {"compilerOptions": {"module": "commonjs"}}}