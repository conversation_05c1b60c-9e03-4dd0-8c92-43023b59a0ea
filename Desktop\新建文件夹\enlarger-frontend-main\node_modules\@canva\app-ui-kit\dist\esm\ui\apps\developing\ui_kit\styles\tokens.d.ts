export declare const tokens: {
    space0: import('../../../../base/html/encode').HtmlEncodedCssList;
    space050: import('../../../../base/html/encode').HtmlEncodedCssList;
    space1: import('../../../../base/html/encode').HtmlEncodedCssList;
    space150: import('../../../../base/html/encode').HtmlEncodedCssList;
    space2: import('../../../../base/html/encode').HtmlEncodedCssList;
    space3: import('../../../../base/html/encode').HtmlEncodedCssList;
    space4: import('../../../../base/html/encode').HtmlEncodedCssList;
    space6: import('../../../../base/html/encode').HtmlEncodedCssList;
    space8: import('../../../../base/html/encode').HtmlEncodedCssList;
    space12: import('../../../../base/html/encode').HtmlEncodedCssList;
    baseUnit: import('../../../../base/html/encode').HtmlEncodedCssList;
    borderRadius: import('../../../../base/html/encode').HtmlEncodedCssList;
    minTouchableArea: import('../../../../base/html/encode').HtmlEncodedCssList;
    shadowSurface: import('../../../../base/html/encode').HtmlEncodedCssList;
    shadowSurfaceHigh: import('../../../../base/html/encode').HtmlEncodedCssList;
    /**  @deprecated use shadowSurface instead. */
    boxShadowFaint: import('../../../../base/html/encode').HtmlEncodedCssList;
    /**  @deprecated use shadowSurface instead. */
    boxShadowMedium: import('../../../../base/html/encode').HtmlEncodedCssList;
    /**  @deprecated use shadowSurfaceHigh instead. */
    boxShadowHeavy: import('../../../../base/html/encode').HtmlEncodedCssList;
    hoverTransition: import('../../../../base/html/encode').HtmlEncodedCssList;
    fadeTransition: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorOverlay: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPrimary: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPrimaryHover: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPrimaryActive: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPrimaryDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPrimaryFore: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPrimaryForeDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorSecondary: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorSecondaryHover: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorSecondaryActive: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorSecondaryDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorSecondaryFore: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorSecondaryForeDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTertiary: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTertiaryHover: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTertiaryActive: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTertiaryDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTertiaryFore: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTertiaryForeDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorContrast: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorContrastHover: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorContrastActive: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorContrastDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorContrastFore: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorContrastForeDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorNeutral: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorNeutralHover: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorNeutralActive: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorNeutralDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorNeutralLow: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorNeutralFore: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorNeutralForeLow: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorNeutralForeDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPositive: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPositiveHover: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPositiveActive: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPositiveDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPositiveLow: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPositiveFore: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPositiveForeLow: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPositiveForeDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorInfo: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorInfoHover: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorInfoActive: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorInfoDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorInfoLow: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorInfoFore: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorInfoForeLow: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorInfoForeDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorWarn: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorWarnHover: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorWarnActive: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorWarnDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorWarnLow: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorWarnFore: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorWarnForeLow: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorWarnForeDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorCritical: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorCriticalHover: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorCriticalActive: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorCriticalDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorCriticalLow: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorCriticalFore: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorCriticalForeLow: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorCriticalForeDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorCanvas: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTabdock: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorPage: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorSurface: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTypographyPrimary: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTypographySecondary: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTypographyTertiary: import('../../../../base/html/encode').HtmlEncodedCssList;
    /**  @deprecated - use `colorTypographyPlaceholder` instead */
    colorTypographyQuaternary: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTypographyPlaceholder: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTypographyPositive: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTypographyCritical: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTypographyCriticalHover: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTypographyCriticalActive: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTypographyWarn: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTypographyInfo: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTypographyLink: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTypographyLinkHover: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorTypographyLinkActive: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorBorder: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorBorderHover: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorBorderActive: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorBorderDisabled: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorBorderCritical: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorBorderLow: import('../../../../base/html/encode').HtmlEncodedCssList;
    colorBorderStrong: import('../../../../base/html/encode').HtmlEncodedCssList;
};
