/* Scroll container */
.scrollContainer {
  box-sizing: border-box;
  overflow-y: scroll;
  height: 100%;
  padding-top: var(--ui-kit-space-2);
  padding-right: var(--ui-kit-space-2);
  padding-bottom: var(--ui-kit-space-2);

  /* for firefox */
  scrollbar-width: thin;
  scrollbar-color: var(--ui-kit-color-typography-quaternary) transparent;
}

.scrollContainer::-webkit-scrollbar {
  position: absolute;
  width: var(--ui-kit-base-unit);
  height: 0;
}

.scrollContainer::-webkit-scrollbar-track {
  background: transparent;
  width: var(--ui-kit-base-unit);
  margin-top: var(--ui-kit-space-1);
  margin-bottom: var(--ui-kit-space-1);
}

.scrollContainer::-webkit-scrollbar-thumb {
  border-radius: var(--ui-kit-border-radius);
  background: var(--ui-kit-color-typography-quaternary);
  visibility: hidden;
}

.scrollContainer:hover::-webkit-scrollbar-thumb,
.scrollContainer:focus::-webkit-scrollbar-thumb,
.scrollContainer:focus-within::-webkit-scrollbar-thumb {
  visibility: visible;
}

.imageCompareContainer {
  width: 100% !important;
  border-radius: 8px !important;
  img {
    object-fit: contain !important;
    width: 100% !important;
    max-height: 200px !important;
    border-radius: 8px !important;
    background-color: var(--ui-kit-color-neutral-low) !important;
  }

  & > div {
    max-height: 200px;
  }
  [data-testid="container"] > :nth-last-child(1) > div {
    background-color: transparent !important;
    padding: 0 !important;
    bottom: 8px !important;
    top: unset !important;
    transform: unset !important;
    right: 8px !important;
  }
  [data-testid="container"] > :nth-last-child(2) > div {
    background-color: transparent !important;
    padding: 0 !important;
    bottom: 8px !important;
    top: unset !important;
    left: 8px !important;
    transform: unset !important;
  }
  [data-testid="container"] > :nth-last-child(3) {
    & > :nth-child(1), & > :nth-child(3) {
      background-color: var(--ui-kit-color-contrast-fore);
    }

    & > :nth-child(2) {
      border-color: var(--ui-kit-color-contrast-fore) !important;;
      & > :nth-child(1) {
        border-right-color: var(--ui-kit-color-contrast-fore) !important;
      }
      & > :nth-child(2) {
        border-left-color: var(--ui-kit-color-contrast-fore) !important;
      }
    }
  }
}

.originImage {
  object-fit: contain !important;
  width: 100% !important;
  max-height: 200px !important;
  border-radius: 8px !important;
  background-color: var(--ui-kit-color-neutral-low) !important;
}
