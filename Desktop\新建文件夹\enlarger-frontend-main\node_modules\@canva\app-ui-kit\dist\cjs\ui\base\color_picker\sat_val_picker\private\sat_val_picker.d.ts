import * as React from 'react';
import { HsvColor } from '../../hsv_color/hsv_color';
import type { DraggableProps } from '../../../draggable/draggable_view';
export type SalValPickerProps = {
    color: HsvColor
    onChange?: (color: HsvColor) => void
    onChangeStart?(): void
    onChangeComplete?(): void
    Draggable: React.ComponentType<DraggableProps>
};
export declare function SatValPicker(
 { color, Draggable, onChange: onChangeProp, onChangeStart, onChangeComplete, }: SalValPickerProps
): import("react/jsx-runtime").JSX.Element;
export declare function mouseToKnobCoords(pickerRect: DomRect, { clientX, clientY }: MouseCoords): KnobCoordsFromCorner;
type DomRect = {
    left: number;
    top: number;
    width: number;
    height: number;
};
type KnobCoordsFromCorner = {
    leftPercent: number;
    topPercent: number;
};
type MouseCoords = {
    clientX: number;
    clientY: number;
};
export {};
