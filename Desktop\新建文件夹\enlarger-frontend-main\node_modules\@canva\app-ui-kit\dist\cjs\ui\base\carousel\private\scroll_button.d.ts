import * as React from 'react';
import type { ScrollState } from '../../scroll_controls/scroll_controls';
type Direction = 'forward' | 'backward';
export type ScrollButtonProps = {
    onClick(): void;
    scrollState: ScrollState
    active?: boolean;
    focus?: boolean;
    direction: Direction;
    scrollableId: string;
    buttonRef?: React.RefObject<HTMLButtonElement | null>;
    oppositeButtonRef?: React.RefObject<HTMLButtonElement | null>;
    startLinkRef?: React.RefObject<HTMLAnchorElement | null>;
    endLinkRef?: React.RefObject<HTMLAnchorElement | null>;
};
export declare const CircularScrollButton: (props: ScrollButtonProps & {
    verticalOffsetPx?: number;
    horizontalOffsetPx?: number;
}) => import("react/jsx-runtime").JSX.Element;
export declare const ChevronScrollButton: (props: ScrollButtonProps & {
    verticalOffsetPx?: number;
}) => import("react/jsx-runtime").JSX.Element;
export {};
