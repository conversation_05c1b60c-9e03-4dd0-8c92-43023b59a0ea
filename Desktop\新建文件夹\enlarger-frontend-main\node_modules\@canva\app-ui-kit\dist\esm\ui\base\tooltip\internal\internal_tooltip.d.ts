import * as React from 'react';
import type { BoxProps } from '../../box/box';
import type { KeyCombination } from '../../key_combinations/key_combinations';
import type { Direction } from '../../provider/provider';
import type { PixelDensitySource } from '../../responsive_image/responsive_image';
import type { ReferenceObject } from '../../surface/pin/pin';
export type Placement = 'top' | 'right' | 'bottom' | 'left';
export type Alignment = 'start' | 'end' | 'center';
export type TriggerProps = {
    tooltipId: string
    tabIndex: 0
    onFocus(): void
    onBlur(): void
    onMouseEnter(): void
    onMouseLeave(): void
    onMouseDown?(): void
};
export type ToggletipTriggerProps = {
    onClick(e: React.MouseEvent<HTMLButtonElement>): void
    onBlur(): void
};
type TooltipConditionalProps = {
    mode?: 'tooltip'
    children?: React.ReactNode | RenderTrigger
    forwardedRef?: React.ForwardedRef<HTMLDivElement>
};
type ToggleTipConditionalProps = {
    mode: 'toggletip'
    children: ToggleTipRenderTrigger
    forwardedRef?: never;
};
type ConditionalProps = TooltipConditionalProps | ToggleTipConditionalProps;
type RenderTrigger = (props: TriggerProps) => React.ReactNode;
type ToggleTipRenderTrigger = (props: ToggletipTriggerProps) => React.ReactNode;
export type BaseTooltipProps = {
    label: string
    align?: Alignment
    shortcut?: KeyCombination
    description?: string
    thumbnail?: string | PixelDensitySource[]
    lineClamp?: number
    expandTooltipWrapper?: boolean
};
type InternalTooltipProps = {
    placement?: Placement
    disabled?: boolean
    closeOnClick?: boolean
} & BaseTooltipProps;
export type TooltipProps = InternalTooltipProps & Pick<TooltipConditionalProps, 'children'>;
export declare function InternalTooltip(props: InternalTooltipProps & ConditionalProps): import("react/jsx-runtime").JSX.Element;
export type TooltipContentProps = Pick<BaseTooltipProps, 'label' | 'align' | 'shortcut' | 'description' | 'thumbnail' | 'lineClamp'> & {
    id?: string
    mode?: 'tooltip' | 'toggletip'
};
export declare function TooltipContent(props: TooltipContentProps): import("react/jsx-runtime").JSX.Element;
export declare function TooltipBox({ className, children, ...props }: BoxProps): import("react/jsx-runtime").JSX.Element;
export type TooltipArrowProps = {
    inset?: boolean
};
export declare function TooltipArrow({ inset }: TooltipArrowProps): import("react/jsx-runtime").JSX.Element;
export type StatelessTooltipProps = TooltipContentProps & BaseTooltipProps & {
    children?: React.ReactNode
    open: boolean
    arrow?: boolean
    refObj?: ReferenceObject
    placement?: AllTooltipPlacements
    direction?: Direction
};
export declare function StatelessTooltip({ children, placement, open, arrow, refObj, direction, ...contentProps }: StatelessTooltipProps): import("react/jsx-runtime").JSX.Element;
type AllTooltipPlacements = 'left-start' | 'right-start' | 'left-end' | 'right-end' | Placement;
export {};
