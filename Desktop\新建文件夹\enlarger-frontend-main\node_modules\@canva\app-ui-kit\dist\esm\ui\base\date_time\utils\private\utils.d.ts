export type TimeMode = 'hour12' | 'hour24';
export type TimePeriod = 'am' | 'pm';
type TimeOpts = {
    hours: number
    minutes: number
};
export type TimeZone = {
    offset: number
    label: string
};
export type DateObj = {
    year: number;
    month: number;
    day: number;
};
export type DateTimeObj = DateObj & {
    hours: number;
    minutes: number;
};
export type DateTimeObject = {
    year: number;
    month: Month;
    day?: number;
    hours?: number;
    minutes?: number;
};
export type DateType = string | number | DateTimeObject | Date;
export type DateFormat = 'MMM YYYY' | 'MMMM' | 'MMMM YYYY' | 'DD MMMM YYYY' | 'DD MMM' | 'DD MMMM' | 'weekday' | 'weekdayShort' | 'hh:mm' | 'h' | 'fullDateTimeYear' | 'fullDateTime' | 'fullDate' | 'short' | 'medium' | 'long' | 'full';
export declare const MINUTE: number;
export declare const HOUR: number;
export declare const DAY: number;
export declare const MONDAY_DATE: Date;
export declare const enum Month {
    JAN = 1,
    FEB = 2,
    MAR = 3,
    APR = 4,
    MAY = 5,
    JUN = 6,
    JUL = 7,
    AUG = 8,
    SEP = 9,
    OCT = 10,
    NOV = 11,
    DEC = 12
}
export declare const enum DayOfWeek {
    MON = 0,
    TUE = 1,
    WED = 2,
    THU = 3,
    FRI = 4,
    SAT = 5,
    SUN = 6
}
export declare function getTimeOfDay(date: DateType): number;
export declare function msToTimeString(ms: number): string;
export declare function timeStringToMs(timeString: string): number;
export declare function dayHours(dayTime: number, timeMode?: TimeMode): number;
export declare function dayMinutes(dayTime: number): number;
export declare function dayPeriod(dayTime: number): TimePeriod;
export declare function toTime(
 opts: {
     timeMode: 'hour24';
 } & TimeOpts
): number;
export declare function toTime(opts: {
    timeMode: 'hour12';
} & TimeOpts & {
    period: TimePeriod;
}): number;
export declare function padTime(time: number): string;
export declare function maxInputHour(timeMode?: TimeMode): number;
export declare function minInputHour(timeMode?: TimeMode): number;
export declare function maxInputMinutes(): number;
export declare function setTimeToDate(date: DateType, dayTime: number): Date;
export declare function setLocalTimeToDate(date: DateType, dayTime: number): Date;
export declare function toDate(date: DateType): Date;
export declare function dateObjToDateString({ year, month, day }: DateObj): string;
export declare function toUTCDateTimeObject(date: DateType): DateTimeObj;
export declare function toDateObject(date: string): DateObj | undefined;
export declare function toLocalDateTimeObject(date: DateType): {
    year: number;
    month: number;
    day: number;
    hours: number;
    minutes: number;
};
export declare function toISO(date: DateType): string;
export declare function getTimestamp(date: DateType): number;
export declare function getDate(date: DateType): number;
export declare function getMonth(date: DateType): Month;
export declare function getYear(date: DateType): number;
export declare function nextMonth(date: DateType): Date;
export declare function prevMonth(date: DateType): Date;
export declare function prevWeek(date: DateType): Date;
export declare function nextWeek(date: DateType): Date;
export declare function nextDay(date: DateType): Date;
export declare function prevDay(date: DateType, daysOffset?: number): Date;
export declare function addDays(date: DateType, daysOffset: number): Date;
export declare function addMonths(date: DateType, monthsOffset: number): Date;
export declare function addYears(date: DateType, numberOfYears: number): Date;
export declare function nextHour(date: DateType): Date;
export declare function prevMinute(date: DateType, minsOffset?: number): Date;
export declare function isEqualDates(date1: DateType, date2: DateType): boolean;
export declare function compareMonthAndYear(date1: DateType, date2: DateType): number;
export declare function compareDays(date1: DateType, date2: DateType): number;
export declare function compareDates(date1: DateType, date2: DateType): number;
export declare function differenceInDays(date1: DateType, date2: DateType, roundingMode?: 'CEIL' | 'FLOOR'): number;
export declare function min<T extends DateType>(...values: T[]): T;
export declare function min(...values: DateType[]): DateType;
export declare function max<T extends DateType>(...values: T[]): T;
export declare function max(...values: DateType[]): DateType;
export declare function checkMonth(date: DateType, month: Month): boolean;
export declare function dayOfWeek(date: DateType): DayOfWeek;
export declare function dayOfWeekStartOfMonth(date: DateType): DayOfWeek;
export declare function numberOfCompleteWeeks(date: DateType): number;
export declare function countDaysInMonth(date: DateType): number;
export declare function monthDays(date: DateType, count?: number): Date[];
export declare function startOfDay(date: DateType): Date;
export declare function format(
 date: DateType,
 locale: string,
 type: DateFormat,
 showInLocalTimezone?: boolean,
 timeMode?: TimeMode
): string;
export declare function key(date: DateType, prefix: string): string;
export declare function fromUTCToTimezone(date: DateType, timezoneOffset: number): Date;
export declare function fromTimezoneToUTC(date: DateType, timezoneOffset: number): Date;
export declare function getTimezone(date: DateType, locale: string): TimeZone;
export declare function getTimezoneOffset(date: DateType): number;
export declare function formatSecondsToTime(timeInSeconds: number): string;
export declare function getTimeModeByLocale(locale: string): TimeMode;
export {};
