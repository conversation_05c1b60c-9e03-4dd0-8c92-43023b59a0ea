{"name": "whatwg-url", "version": "11.0.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "main": "index.js", "files": ["index.js", "webidl2js-wrapper.js", "lib/*.js"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "jsdom/whatwg-url", "dependencies": {"tr46": "^3.0.0", "webidl-conversions": "^7.0.0"}, "devDependencies": {"@domenic/eslint-config": "^1.4.0", "benchmark": "^2.1.4", "browserify": "^17.0.0", "domexception": "^4.0.0", "eslint": "^7.32.0", "got": "^11.8.2", "jest": "^27.2.4", "webidl2js": "^17.0.0"}, "engines": {"node": ">=12"}, "scripts": {"coverage": "jest --coverage", "lint": "eslint .", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js", "test": "jest"}, "jest": {"collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}}