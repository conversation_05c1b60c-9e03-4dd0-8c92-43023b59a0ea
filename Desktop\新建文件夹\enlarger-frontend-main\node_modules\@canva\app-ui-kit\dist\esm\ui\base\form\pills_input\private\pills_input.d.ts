import * as React from 'react';
import type { Icon } from '../../../icons/icons';
import type { KeyCombination } from '../../../key_combinations/key_combinations';
import type { InputPillMaxWidth } from './input_pill';
type Role = 'combobox' | 'textbox';
export type PillsInputItem = {
    value: string;
    isLoading?: boolean;
    icon?: Icon;
    state?: 'default' | 'warning' | 'error';
    onRemoveClick?(): void;
};
export type PillsInputProps = {
    value: PillsInputItem[]
    inputValue: string
    allowDuplicates?: 'always' | 'case-sensitive' | 'never'
    delimiters?: string[]
    disabled?: boolean
    searchIcon?: Icon
    loading?: boolean
    error?: boolean
    borderless?: boolean
    placeholder?: string
    additionalPlaceholder?: string
    autoFocus?: 'on-desktop' | 'always'
    maxLength?: number
    minRows?: number
    maxRows?: number
    maxPillWidth?: InputPillMaxWidth
    addPillKeyCombinations?: KeyCombination[]
    onInputChange?(value: string): void
    onPillAdd?(value: string): void
    onLastPillRemove?(): void
    onClick?(): void
    onClearClick?(): void
    onBlur?(): void
    onFocus?(): void
    onPaste?(e: React.ClipboardEvent<HTMLInputElement>): void
    ariaLabel?: string
    id?: string
    role?: Role
    inputMode?: React.HTMLAttributes<HTMLInputElement>['inputMode']
    ariaControls?: string
    ariaLabelledBy?: string
    ariaDescribedBy?: string
    ariaExpanded?: boolean
    ariaHasPopup?: React.AriaAttributes['aria-haspopup']
    ariaOwns?: string
    ariaAutocomplete?: React.AriaAttributes['aria-autocomplete']
    ariaActiveDescendant?: string
};
export declare class PillsInput extends React.Component<PillsInputProps> {
    private readonly dynamicSizeInputRef;
    private readonly inputRef;
    private srStatusStrings;
    private isFocused;
    static readonly defaultProps: Partial<PillsInputProps>;
    private readonly onMouseDown;
    private readonly onPillAdd;
    private readonly onFocus;
    private readonly onBlur;
    state: {
        isPastingText: boolean;
    };
    private readonly onPaste;
    readonly focus: () => void;
    readonly getPillTone: (state: PillsInputItem["state"]) => "warn" | "critical" | "secondary";
    componentDidUpdate(prevProps: PillsInputProps): void;
    private readonly valueExists;
    render(): import("react/jsx-runtime").JSX.Element;
    private renderClearIcon;
    private shouldRenderClearButton;
    private maybeRenderClearIcon;
}
export {};
