{"name": "canva-enlarger", "description": "A boilerplate for creating Canva Apps.", "engines": {"node": "^18 || ^20.10.0", "npm": "^9 || ^10"}, "scripts": {"start": "ts-node ./scripts/start/start.ts", "build": "webpack --config webpack.config.js --mode production", "lint:types": "tsc", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier '{examples,src,utils,scripts}/**/*.{css,ts,tsx,json,js}' --no-config --write", "format:check": "prettier '{examples,src,utils,scripts}/**/*.{css,ts,tsx,json,js}' --no-config --check --ignore-path", "format:file": "prettier $1 --no-config --write", "test": "jest --no-cache", "test:watch": "jest --no-cache --watch"}, "keywords": [], "author": "Canva Pty Ltd.", "license": "SEE LICENSE IN LICENSE.md", "private": true, "workspaces": ["./examples/*"], "dependencies": {"@canva/app-ui-kit": "^4.0.0", "@canva/asset": "^2.0.0", "@canva/design": "^2.1.0", "@tanstack/react-query": "^5.51.21", "react": "^18.3.1", "react-compare-image": "^3.4.0", "react-compare-slider": "^3.1.0", "react-dom": "^18.3.1", "react-helmet": "^6.1.0"}, "devDependencies": {"@eslint/js": "^9.6.0", "@ngrok/ngrok": "^1.1.0", "@svgr/webpack": "^8.0.1", "@testing-library/dom": "^10.2.0", "@testing-library/react": "^16.0.0", "@types/debug": "^4.1.7", "@types/express": "^4.17.13", "@types/jest": "^29.4.0", "@types/jsonwebtoken": "^9.0.1", "@types/node": "^20.10.0", "@types/node-fetch": "^2.6.2", "@types/node-forge": "^1.3.1", "@types/nodemon": "^1.19.2", "@types/prompts": "^2.4.2", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.11", "@types/webpack-env": "^1.18.0", "@typescript-eslint/eslint-plugin": "^7.6.0", "@typescript-eslint/parser": "^7.6.0", "chalk": "^4.1.2", "cli-table3": "^0.6.2", "css-loader": "^6.7.1", "css-modules-typescript-loader": "^4.0.1", "cssnano": "^6.0.1", "debug": "^4.3.4", "dotenv": "^16.0.1", "eslint": "^8.57.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "exponential-backoff": "^3.1.0", "express": "^4.18.1", "express-basic-auth": "^1.2.1", "jest": "^29.7.0", "jest-css-modules-transform": "^4.4.2", "jest-environment-jsdom": "^29.7.0", "jsonwebtoken": "^9.0.0", "jwks-rsa": "^3.0.1", "mini-css-extract-plugin": "^2.6.1", "node-fetch": "^2.6.7", "node-forge": "^1.3.1", "nodemon": "3.0.1", "postcss-loader": "^7.3.3", "prettier": "^2.7.1", "prompts": "^2.4.2", "style-loader": "^3.3.1", "terser-webpack-plugin": "^5.3.5", "ts-jest": "^29.0.5", "ts-loader": "^9.3.1", "ts-node": "^10.9.1", "typescript": "^5.2.2", "url-loader": "^4.1.1", "webpack": "^5.74.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.10.0", "yargs": "^17.5.1"}}