import * as React from 'react';
import type { ButtonProps } from '../../../button/button';
import type { Icon } from '../../../icons/icons';
import type { FlyoutProps, HeaderAndFooterProps, TriggerProps, Width as FlyoutWidth } from '../../../surface/flyout/flyout';
import type { AreEqualFn, GetLabelFn, IsSelectedFn, SelectOption, SelectOptionGroup } from './base_select_util';
type PickRequired<T, U extends keyof T> = Required<Pick<T, U>>;
export type StatelessBaseSelectProps<T> = {
    trigger?: (props: BaseSelectTriggerProps<T>) => React.ReactNode
    Trigger?: React.ComponentType<BaseSelectTriggerProps<T>>
    menu?: (props: BaseSelectMenuProps<T>) => React.ReactNode
    Menu?: React.ComponentType<BaseSelectMenuProps<T>>
    item?: (props: BaseSelectItemProps<T>) => React.ReactNode
    Item?: React.ComponentType<BaseSelectItemProps<T>>
    Flyout?: React.ComponentType<FlyoutProps>
    options: (SelectOption<T> | SelectOptionGroup<T>)[]
    flatOptions?: SelectOption<T>[]
    optionGroups?: SelectOptionGroup<T>[]
    selectedOptions?: SelectOption<T>[]
    value?: T[] | T | undefined
    open?: boolean
    activeIndex?: number
    placeholder?: string
    disabled?: boolean
    error?: boolean
    triggerContainerRef?: React.Ref<HTMLDivElement>
    menuContainerRef?: React.Ref<HTMLDivElement>
    className?: string
    stretch?: boolean
    animateFlyout?: boolean
    focusOnMenu?: boolean
    flyoutPlacement?: 'bottom-start' | 'bottom-end'
    flyoutWidth?: FlyoutWidth
    blockOutsidePointerEvents?: boolean
    id?: string
    ariaLabel?: string
    ariaLabelledBy?: string
    ariaDescribedBy?: string
    tooltipLabel?: ButtonProps['tooltipLabel']
    tooltipPlacement?: ButtonProps['tooltipPlacement']
    tooltipDisabled?: ButtonProps['tooltipDisabled']
    areEqual?: AreEqualFn<T>
    isSelected?: IsSelectedFn<T>
    getLabel?: GetLabelFn<T>
    onBlur?(): void
    onFocus?(): void
    onRequestClose(): void
    onRequestOpen(): void
    onItemClick?(option: SelectOption<T>, event?: React.MouseEvent<any>): void
    onItemHover?(option: SelectOption<T>, event?: React.MouseEvent<any>): void
} & Pick<HeaderAndFooterProps, 'title' | 'headerEnd'>;
export type BaseSelectTriggerProps<T> = Pick<StatelessBaseSelectProps<T>, 'options' | 'value' | 'disabled' | 'error' | 'placeholder' | 'activeIndex' | 'id' | 'ariaLabel' | 'ariaLabelledBy' | 'ariaDescribedBy' | 'tooltipLabel' | 'tooltipPlacement' | 'tooltipDisabled' | 'onBlur' | 'onFocus'> & PickRequired<StatelessBaseSelectProps<T>, 'getLabel' | 'isSelected' | 'flatOptions' | 'optionGroups' | 'selectedOptions'> & {
    activeOption?: SelectOption<T> | undefined
    className?: string
    children?: React.ReactNode
    Icon?: 'auto' | 'none' | Icon
    role: 'combobox'
    labelId: string
    open?: boolean
    ariaActiveDescendant?: string
    onRequestToggle(): void
} & Pick<TriggerProps, 'disclosure' | 'active' | 'ariaControls'>;
export type BaseSelectMenuProps<T> = Pick<StatelessBaseSelectProps<T>, 'Item' | 'item' | 'value' | 'options' | 'activeIndex' | 'ariaLabel' | 'ariaLabelledBy' | 'onItemClick' | 'onItemHover' | 'menuContainerRef'> & PickRequired<StatelessBaseSelectProps<T>, 'areEqual' | 'isSelected' | 'getLabel' | 'flatOptions' | 'optionGroups' | 'selectedOptions'> & {
    sizingMode?: 'fixed' | 'fill'
    activeOption?: SelectOption<T> | undefined
    id: string
    className?: string
    header?: React.ReactNode
    footer?: React.ReactNode
    children?: React.ReactNode
};
export type BaseSelectItemProps<T> = React.PropsWithChildren<{
    id: string
    option: SelectOption<T>
    selected: boolean
    active: boolean
    forwardedRef?: React.Ref<HTMLLIElement>
    start?: React.ReactNode
    end?: React.ReactNode
    className?: string
    multiSelectable?: boolean
} & Pick<BaseSelectMenuProps<T>, 'getLabel' | 'onItemClick' | 'onItemHover'>>;
export declare function StatelessBaseSelect<T>(props: StatelessBaseSelectProps<T>): import("react/jsx-runtime").JSX.Element;
export declare function BaseSelectTrigger<T>(props: BaseSelectTriggerProps<T>): import("react/jsx-runtime").JSX.Element;
export declare function BaseSelectMenu<T>(props: BaseSelectMenuProps<T>): import("react/jsx-runtime").JSX.Element;
export type WindowedSelectMenuProps<T> = BaseSelectMenuProps<T> & {
    itemHeightMultiplier?: number
};
export declare function WindowedSelectMenu<T>(props: WindowedSelectMenuProps<T>): import("react/jsx-runtime").JSX.Element;
export declare function renderEmptyMenuContent(): import("react/jsx-runtime").JSX.Element;
export declare const BaseSelectItem: typeof BaseSelectItem_;
declare function BaseSelectItem_<T>(props: BaseSelectItemProps<T>): import("react/jsx-runtime").JSX.Element;
export {};
