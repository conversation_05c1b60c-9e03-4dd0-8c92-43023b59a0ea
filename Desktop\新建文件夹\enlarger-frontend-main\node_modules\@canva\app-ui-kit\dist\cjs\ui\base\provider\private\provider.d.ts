import * as React from 'react';
export type Direction = 'LTR' | 'RTL';
export type Theme = 'ADAPTIVE' | 'LIGHT' | 'DARK' | undefined;
export type AutoplayVideos = 'ADAPTIVE' | 'DONT_AUTOPLAY' | 'AUTOPLAY' | undefined;
export type EaselConfiguration = {
    enableAnimations: boolean
    direction: Direction
    disableFocusTraps: boolean
    enableUserSelection: boolean
    theme?: Theme
    enableHighColorContrast?: boolean
    autoplayVideos?: AutoplayVideos
    disableDialogBlur: boolean
    expectedPixelsPerRem?: number
};
export declare function EaselProvider({ enableAnimations, direction, disableFocusTraps, enableUserSelection, theme, enableHighColorContrast, autoplayVideos, disableDialogBlur, expectedPixelsPerRem, children, }: EaselConfiguration & {
    children: React.ReactNode
}): import("react/jsx-runtime").JSX.Element;
export declare function useEnableAnimations(): boolean;
export declare function useDirection(): Direction;
export declare function useDisableFocusTraps(): boolean;
export declare function useEnableUserSelection(): boolean;
export declare function useTheme(): Theme;
export declare function useEnableHighColorContrast(): boolean | undefined;
export declare function useAutoplayVideos(): AutoplayVideos;
export declare function useDisableDialogBlur(): boolean;
export declare function useExpectedPixelsPerRem(): number | undefined;
