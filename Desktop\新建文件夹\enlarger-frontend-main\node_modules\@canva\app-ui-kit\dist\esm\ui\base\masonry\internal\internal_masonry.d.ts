import type { MasonryPresenter, MeasuredRow } from '../masonry_presenter';
import type { RowVisibility } from '../masonry_virtualization_controller';
type MasonryContainerProps = {
    rows: MeasuredRow[];
    rowVisibility?: RowVisibility;
    containerWidth?: number;
    horizontalGutterPx: number;
    verticalGutterPx: number;
    presenter?: MasonryPresenter;
};
export declare const InternalMasonryContainer: (({ rows, rowVisibility, presenter, verticalGutterPx, horizontalGutterPx, containerWidth, }: MasonryContainerProps) => import("react/jsx-runtime").JSX.Element) & {
    displayName: string;
};
export {};
