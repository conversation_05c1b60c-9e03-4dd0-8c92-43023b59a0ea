import * as React from 'react';
import type { BaseTooltipProps, StatelessTooltipProps, TooltipProps } from '../internal/internal_tooltip';
import type { TooltipState } from './tooltip_presenter';
export declare const Tooltip: React.ForwardRefExoticComponent<{
    placement?: import('../internal/internal_tooltip').Placement;
    disabled?: boolean;
    closeOnClick?: boolean;
} & BaseTooltipProps & Pick<{
    mode?: "tooltip";
    children?: React.ReactNode | ((props: import('../internal/internal_tooltip').TriggerProps) => React.ReactNode);
    forwardedRef?: React.ForwardedRef<HTMLDivElement>;
}, "children"> & React.RefAttributes<HTMLDivElement>>;
export type TriggeredTooltipProps = Pick<TooltipProps, 'placement'> & Pick<StatelessTooltipProps, 'arrow' | 'children'> & BaseTooltipProps & {
    state: TooltipState
    autoclose?: boolean
};
export declare const TriggeredTooltip: ((props: TriggeredTooltipProps) => import("react/jsx-runtime").JSX.Element) & {
    displayName: string;
};
type FloatingTooltipInfo = Pick<StatelessTooltipProps, 'label' | 'placement' | 'direction'> & {
    x: number
    y: number
};
export type FloatingTooltipProps = React.PropsWithChildren<FloatingTooltipInfo & Pick<StatelessTooltipProps, 'arrow' | 'open'>>;
export declare const FloatingTooltip: ((props: FloatingTooltipProps) => import("react/jsx-runtime").JSX.Element) & {
    displayName: string;
};
export {};
