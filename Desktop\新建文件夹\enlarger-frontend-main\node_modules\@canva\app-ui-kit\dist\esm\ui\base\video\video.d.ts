import * as React from 'react';
import { prefersReducedMotion as prefersReducedMotionBase } from '../animation/supports_animation';
import type { AutoplayVideos } from '../provider/provider';
type Props = React.VideoHTMLAttributes<HTMLVideoElement> & {
    playbackRate?: number;
    ariaLabel?: string;
    ariaHidden?: boolean;
};
export declare function createVideoComponent(cleanup?: (videoElement: HTMLVideoElement) => void): React.ComponentType<React.PropsWithoutRef<Props> & React.RefAttributes<HTMLVideoElement>>;
export declare function createVideoA11ySafeComponent(
    Video: React.ComponentType<Props & React.RefAttributes<HTMLVideoElement>>,
    prefersReducedMotion?: typeof prefersReducedMotionBase
): React.ForwardRefExoticComponent<React.VideoHTMLAttributes<HTMLVideoElement> & {
    playbackRate?: number;
    ariaLabel?: string;
    ariaHidden?: boolean;
} & React.RefAttributes<HTMLVideoElement>>;
export declare function canA11ySafeVideoAutoplay({ prefersReducedMotion, autoplayVideos, enableAnimations, }: {
    prefersReducedMotion?: () => boolean;
    autoplayVideos: AutoplayVideos;
    enableAnimations: boolean;
}): boolean;
export declare const Video: React.ComponentType<React.VideoHTMLAttributes<HTMLVideoElement> & {
    playbackRate?: number;
    ariaLabel?: string;
    ariaHidden?: boolean;
} & React.RefAttributes<HTMLVideoElement>>;
export declare const VideoA11ySafe: React.ForwardRefExoticComponent<React.VideoHTMLAttributes<HTMLVideoElement> & {
    playbackRate?: number;
    ariaLabel?: string;
    ariaHidden?: boolean;
} & React.RefAttributes<HTMLVideoElement>>;
export {};
