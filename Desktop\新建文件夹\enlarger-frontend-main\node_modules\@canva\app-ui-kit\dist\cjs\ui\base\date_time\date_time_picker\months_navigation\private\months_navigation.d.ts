import * as React from 'react';
import type { DateTimeObject, Month } from '../../../utils/utils';
export type DisableButtonsType = 'prev' | 'next' | 'both';
export type MonthsNavigationProps = {
    locale: string
    month: Month
    year: number
    onChange: (date: DateTimeObject) => void
    today?: DateTimeObject;
    disabled?: DisableButtonsType
    animate: boolean
    titleId?: string
};
export declare class MonthsNavigation extends React.PureComponent<MonthsNavigationProps> {
    private prevDate;
    private animateKey;
    private titleRef?;
    protected static readonly defaultProps: Partial<MonthsNavigationProps>;
    componentDidUpdate(prevProps: Readonly<MonthsNavigationProps>): void;
    private readonly onTitleRef;
    private get today();
    private get date();
    private getTitleText;
    private readonly onReset;
    private isCurrentMonth;
    private readonly onPreviousMonth;
    private readonly onNextMonth;
    private get disabled();
    private get prevDisabled();
    private get nextDisabled();
    private renderTitle;
    private renderAnimatedTitle;
    render(): import("react/jsx-runtime").JSX.Element;
}
