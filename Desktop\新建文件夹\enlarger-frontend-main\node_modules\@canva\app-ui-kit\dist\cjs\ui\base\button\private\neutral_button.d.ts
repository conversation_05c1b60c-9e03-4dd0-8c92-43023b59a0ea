import * as React from 'react';
import type { But<PERSON><PERSON>and<PERSON> } from '../button';
import type { AnchorProps, ButtonProps } from './internal_button';
type NeutralButtonVariant = 'primary' | 'secondary' | 'tertiary';
type VariantProp = {
    variant: NeutralButtonVariant;
};
export declare const NeutralButton: React.ForwardRefExoticComponent<React.PropsWithoutRef<ButtonProps & VariantProp> & React.RefAttributes<ButtonHandle>>;
export declare const NeutralButtonLink: React.ForwardRefExoticComponent<React.PropsWithoutRef<AnchorProps & VariantProp> & React.RefAttributes<ButtonHandle>>;
export {};
