import * as React from 'react';
import type { BackButtonEvent } from '../../../mobile_event_handler/mobile_event_handler';
import type { SurfaceHeaderProps } from '../../header/header';
import type { ContentContainerProps } from '../../internal/content_container';
export type { SnapPoint } from '../behavior/behavior';
export type ScreenHeight = 'half' | 'full';
export type AutoHeight = {
    autoUpTo: ScreenHeight;
};
export type OnRequestCloseContext = {
    action: 'resize' | 'backButton' | 'backdrop';
};
export type Height = ScreenHeight | AutoHeight | 'auto';
export type DragHandleVisibility = 'auto' | 'never';
export type SheetProps = {
    open: boolean
    onBackButton?(event: BackButtonEvent): void
} & SharedSheetProps;
export type SheetImperativeHandle = {
    setHeight: (height: Height) => void
} & SheetContentImperativeHandle;
export type HeaderAndFooterProps = Pick<SurfaceHeaderProps, 'title' | 'description'> & {
    headerStart?: SurfaceHeaderProps['start']
    headerEnd?: SurfaceHeaderProps['end'] | 'none'
    headerDivider?: 'always' | 'when-scrolled' | 'never'
    headerAlignment?: SurfaceHeaderProps['alignment']
    header?: React.ReactNode
    footer?: React.ReactNode
};
type EventHandlerProps = {
    onRequestClose?: (context?: OnRequestCloseContext) => void
    onOpenComplete?: () => void
    onCloseComplete?: () => void
    onScroll?: (arg: {
        scrollTop: number;
    }) => void
};
type SharedSheetProps = {
    height?: Height | readonly Height[]
    scrollToResizeBelow?: Height
    backdropVisible?: boolean
    handleVisible?: DragHandleVisibility
    handleSafeAreaInsetBottom?: boolean
    id?: string
    children?: React.ReactNode
} & HeaderAndFooterProps & ContentContainerProps & EventHandlerProps;
export declare const Sheet: React.ForwardRefExoticComponent<SheetProps & React.RefAttributes<SheetImperativeHandle>>;
type SheetContentImperativeHandle = {
    scrollTo: (options: ScrollToOptions) => void
    getBoundingClientRect: () => DOMRect | undefined
    scrollTop: number | undefined
    scrollLeft: number | undefined
    scrollHeight: number | undefined
    scrollWidth: number | undefined
    clientHeight: number | undefined
    clientWidth: number | undefined
    offsetHeight: number | undefined
    offsetWidth: number | undefined
};
type ScrollToOptions = {
    top?: number;
    left?: number;
    behavior?: 'auto' | 'smooth' | 'instant';
} | number;
