import * as React from 'react';
import type { MeasuredComponentProps } from 'react-measure';
import type { Layer } from './layers';
export declare const ANIMATION_TIME: number;
type LayerViewProps = {
    layer: Layer;
    onLayerClose(layer: Layer): void;
    window: Window
};
export declare class LayerViewUnMeasured extends React.Component<LayerViewProps & MeasuredComponentProps> {
    private containerRef;
    private currentRef?;
    private currentFocused?;
    private viewContainerStyle;
    private viewportResizeDirection;
    private didViewportResize;
    private trackedMeasure;
    private visibleAreaSize;
    private initialTransform;
    private initialMaxHeight;
    private get safeAreaInsetTop();
    private readonly onDraggableHandlePanStart;
    private readonly onDraggableHandlePanMove;
    private readonly onDraggableHandlePanEnd;
    private readonly recognizers?;
    private get maxVisibleSize();
    private get measuredSize();
    private get viewportSize();
    private maybeGetRecognizers;
    private readonly onClickOverlay;
    render(): import("react/jsx-runtime").JSX.Element;
    componentDidMount(): void;
    private updateViewContainerStyle;
    componentDidUpdate(): void;
    private maybeTriggerTopOfLayerChange;
    private contentParentLayerContainer;
    private createLayerContainer;
    private viewContainerReactionDisposer;
    private readonly viewContainerRef;
    private readonly previousContentRef;
    private readonly currentContentRef;
    private readonly previousOuterContentRef;
    private readonly currentOuterContentRef;
    private getContainerSize;
    private get sheetContentContainerStyles();
    private getContainerStyles;
    private getOuterContentStyles;
    private readonly onPreviousTransitionEnd;
    private readonly onContainerTransitionEnd;
    private readonly onSafeAreaResize;
}
export declare const LayerView: React.ComponentType<LayerViewProps>;
export {};
