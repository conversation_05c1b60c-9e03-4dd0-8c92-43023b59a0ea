{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\nimport { NodePath, types as t } from '@babel/core'\n\ninterface State {\n  replacedComponents: Set<string>\n  unsupportedComponents: Set<string>\n}\n\nconst elementToComponent: { [key: string]: string } = {\n  svg: 'Svg',\n  circle: 'Circle',\n  clipPath: 'ClipPath',\n  ellipse: 'Ellipse',\n  g: 'G',\n  linearGradient: 'LinearGradient',\n  radialGradient: 'RadialGradient',\n  line: 'Line',\n  path: 'Path',\n  pattern: 'Pattern',\n  polygon: 'Polygon',\n  polyline: 'Polyline',\n  rect: 'Rect',\n  symbol: 'Symbol',\n  text: 'Text',\n  textPath: 'TextPath',\n  tspan: 'TSpan',\n  use: 'Use',\n  defs: 'Defs',\n  stop: 'Stop',\n  mask: 'Mask',\n  image: 'Image',\n  foreignObject: 'ForeignObject',\n}\n\nconst plugin = () => {\n  function replaceElement(path: NodePath<t.JSXElement>, state: State) {\n    const namePath = path.get('openingElement').get('name')\n    if (!namePath.isJSXIdentifier()) return\n    const { name } = namePath.node\n\n    // Replace element by react-native-svg components\n    const component = elementToComponent[name]\n\n    if (component) {\n      namePath.replaceWith(t.jsxIdentifier(component))\n      if (path.has('closingElement')) {\n        const closingNamePath = path\n          .get('closingElement')\n          .get('name') as NodePath<t.JSXIdentifier>\n        closingNamePath.replaceWith(t.jsxIdentifier(component))\n      }\n      state.replacedComponents.add(component)\n      return\n    }\n\n    // Remove element if not supported\n    state.unsupportedComponents.add(name)\n    path.remove()\n  }\n\n  const svgElementVisitor = {\n    JSXElement(path: NodePath<t.JSXElement>, state: State) {\n      if (\n        !path.get('openingElement').get('name').isJSXIdentifier({ name: 'svg' })\n      ) {\n        return\n      }\n\n      replaceElement(path, state)\n      path.traverse(jsxElementVisitor, state)\n    },\n  }\n\n  const jsxElementVisitor = {\n    JSXElement(path: NodePath<t.JSXElement>, state: State) {\n      replaceElement(path, state)\n    },\n  }\n\n  const importDeclarationVisitor = {\n    ImportDeclaration(path: NodePath<t.ImportDeclaration>, state: State) {\n      if (\n        path.get('source').isStringLiteral({ value: 'react-native-svg' }) &&\n        !path.get('importKind').hasNode()\n      ) {\n        state.replacedComponents.forEach((component) => {\n          if (\n            path\n              .get('specifiers')\n              .some((specifier) =>\n                specifier.get('local').isIdentifier({ name: component }),\n              )\n          ) {\n            return\n          }\n\n          path.pushContainer(\n            'specifiers',\n            t.importSpecifier(t.identifier(component), t.identifier(component)),\n          )\n        })\n      } else if (path.get('source').isStringLiteral({ value: 'expo' })) {\n        path.pushContainer(\n          'specifiers',\n          t.importSpecifier(t.identifier('Svg'), t.identifier('Svg')),\n        )\n      } else {\n        return\n      }\n\n      if (state.unsupportedComponents.size && !path.has('trailingComments')) {\n        const componentList = [...state.unsupportedComponents].join(', ')\n        path.addComment(\n          'trailing',\n          ` SVGR has dropped some elements not supported by react-native-svg: ${componentList} `,\n        )\n      }\n    },\n  }\n\n  return {\n    visitor: {\n      Program(path: NodePath<t.Program>, state: Partial<State>) {\n        state.replacedComponents = new Set()\n        state.unsupportedComponents = new Set()\n\n        path.traverse(svgElementVisitor, state as State)\n        path.traverse(importDeclarationVisitor, state as State)\n      },\n    },\n  }\n}\n\nexport default plugin\n"], "names": ["t"], "mappings": ";;;;AAQA,MAAM,kBAAgD,GAAA;AAAA,EACpD,GAAK,EAAA,KAAA;AAAA,EACL,MAAQ,EAAA,QAAA;AAAA,EACR,QAAU,EAAA,UAAA;AAAA,EACV,OAAS,EAAA,SAAA;AAAA,EACT,CAAG,EAAA,GAAA;AAAA,EACH,cAAgB,EAAA,gBAAA;AAAA,EAChB,cAAgB,EAAA,gBAAA;AAAA,EAChB,IAAM,EAAA,MAAA;AAAA,EACN,IAAM,EAAA,MAAA;AAAA,EACN,OAAS,EAAA,SAAA;AAAA,EACT,OAAS,EAAA,SAAA;AAAA,EACT,QAAU,EAAA,UAAA;AAAA,EACV,IAAM,EAAA,MAAA;AAAA,EACN,MAAQ,EAAA,QAAA;AAAA,EACR,IAAM,EAAA,MAAA;AAAA,EACN,QAAU,EAAA,UAAA;AAAA,EACV,KAAO,EAAA,OAAA;AAAA,EACP,GAAK,EAAA,KAAA;AAAA,EACL,IAAM,EAAA,MAAA;AAAA,EACN,IAAM,EAAA,MAAA;AAAA,EACN,IAAM,EAAA,MAAA;AAAA,EACN,KAAO,EAAA,OAAA;AAAA,EACP,aAAe,EAAA,eAAA;AACjB,CAAA,CAAA;AAEA,MAAM,SAAS,MAAM;AACnB,EAAS,SAAA,cAAA,CAAe,MAA8B,KAAc,EAAA;AAClE,IAAA,MAAM,WAAW,IAAK,CAAA,GAAA,CAAI,gBAAgB,CAAA,CAAE,IAAI,MAAM,CAAA,CAAA;AACtD,IAAI,IAAA,CAAC,SAAS,eAAgB,EAAA;AAAG,MAAA,OAAA;AACjC,IAAM,MAAA,EAAE,IAAK,EAAA,GAAI,QAAS,CAAA,IAAA,CAAA;AAG1B,IAAM,MAAA,SAAA,GAAY,mBAAmB,IAAI,CAAA,CAAA;AAEzC,IAAA,IAAI,SAAW,EAAA;AACb,MAAA,QAAA,CAAS,WAAY,CAAAA,UAAA,CAAE,aAAc,CAAA,SAAS,CAAC,CAAA,CAAA;AAC/C,MAAI,IAAA,IAAA,CAAK,GAAI,CAAA,gBAAgB,CAAG,EAAA;AAC9B,QAAA,MAAM,kBAAkB,IACrB,CAAA,GAAA,CAAI,gBAAgB,CAAA,CACpB,IAAI,MAAM,CAAA,CAAA;AACb,QAAA,eAAA,CAAgB,WAAY,CAAAA,UAAA,CAAE,aAAc,CAAA,SAAS,CAAC,CAAA,CAAA;AAAA,OACxD;AACA,MAAM,KAAA,CAAA,kBAAA,CAAmB,IAAI,SAAS,CAAA,CAAA;AACtC,MAAA,OAAA;AAAA,KACF;AAGA,IAAM,KAAA,CAAA,qBAAA,CAAsB,IAAI,IAAI,CAAA,CAAA;AACpC,IAAA,IAAA,CAAK,MAAO,EAAA,CAAA;AAAA,GACd;AAEA,EAAA,MAAM,iBAAoB,GAAA;AAAA,IACxB,UAAA,CAAW,MAA8B,KAAc,EAAA;AACrD,MAAA,IACE,CAAC,IAAA,CAAK,GAAI,CAAA,gBAAgB,CAAE,CAAA,GAAA,CAAI,MAAM,CAAA,CAAE,eAAgB,CAAA,EAAE,IAAM,EAAA,KAAA,EAAO,CACvE,EAAA;AACA,QAAA,OAAA;AAAA,OACF;AAEA,MAAA,cAAA,CAAe,MAAM,KAAK,CAAA,CAAA;AAC1B,MAAK,IAAA,CAAA,QAAA,CAAS,mBAAmB,KAAK,CAAA,CAAA;AAAA,KACxC;AAAA,GACF,CAAA;AAEA,EAAA,MAAM,iBAAoB,GAAA;AAAA,IACxB,UAAA,CAAW,MAA8B,KAAc,EAAA;AACrD,MAAA,cAAA,CAAe,MAAM,KAAK,CAAA,CAAA;AAAA,KAC5B;AAAA,GACF,CAAA;AAEA,EAAA,MAAM,wBAA2B,GAAA;AAAA,IAC/B,iBAAA,CAAkB,MAAqC,KAAc,EAAA;AACnE,MAAA,IACE,KAAK,GAAI,CAAA,QAAQ,CAAE,CAAA,eAAA,CAAgB,EAAE,KAAO,EAAA,kBAAA,EAAoB,CAAA,IAChE,CAAC,IAAK,CAAA,GAAA,CAAI,YAAY,CAAA,CAAE,SACxB,EAAA;AACA,QAAM,KAAA,CAAA,kBAAA,CAAmB,OAAQ,CAAA,CAAC,SAAc,KAAA;AAC9C,UACE,IAAA,IAAA,CACG,GAAI,CAAA,YAAY,CAChB,CAAA,IAAA;AAAA,YAAK,CAAC,SACL,KAAA,SAAA,CAAU,GAAI,CAAA,OAAO,EAAE,YAAa,CAAA,EAAE,IAAM,EAAA,SAAA,EAAW,CAAA;AAAA,WAE3D,EAAA;AACA,YAAA,OAAA;AAAA,WACF;AAEA,UAAK,IAAA,CAAA,aAAA;AAAA,YACH,YAAA;AAAA,YACAA,UAAA,CAAE,gBAAgBA,UAAE,CAAA,UAAA,CAAW,SAAS,CAAG,EAAAA,UAAA,CAAE,UAAW,CAAA,SAAS,CAAC,CAAA;AAAA,WACpE,CAAA;AAAA,SACD,CAAA,CAAA;AAAA,OACH,MAAA,IAAW,IAAK,CAAA,GAAA,CAAI,QAAQ,CAAA,CAAE,gBAAgB,EAAE,KAAA,EAAO,MAAO,EAAC,CAAG,EAAA;AAChE,QAAK,IAAA,CAAA,aAAA;AAAA,UACH,YAAA;AAAA,UACAA,UAAA,CAAE,gBAAgBA,UAAE,CAAA,UAAA,CAAW,KAAK,CAAG,EAAAA,UAAA,CAAE,UAAW,CAAA,KAAK,CAAC,CAAA;AAAA,SAC5D,CAAA;AAAA,OACK,MAAA;AACL,QAAA,OAAA;AAAA,OACF;AAEA,MAAA,IAAI,MAAM,qBAAsB,CAAA,IAAA,IAAQ,CAAC,IAAK,CAAA,GAAA,CAAI,kBAAkB,CAAG,EAAA;AACrE,QAAA,MAAM,gBAAgB,CAAC,GAAG,MAAM,qBAAqB,CAAA,CAAE,KAAK,IAAI,CAAA,CAAA;AAChE,QAAK,IAAA,CAAA,UAAA;AAAA,UACH,UAAA;AAAA,UACA,CAAsE,mEAAA,EAAA,aAAA,CAAA,CAAA,CAAA;AAAA,SACxE,CAAA;AAAA,OACF;AAAA,KACF;AAAA,GACF,CAAA;AAEA,EAAO,OAAA;AAAA,IACL,OAAS,EAAA;AAAA,MACP,OAAA,CAAQ,MAA2B,KAAuB,EAAA;AACxD,QAAM,KAAA,CAAA,kBAAA,uBAAyB,GAAI,EAAA,CAAA;AACnC,QAAM,KAAA,CAAA,qBAAA,uBAA4B,GAAI,EAAA,CAAA;AAEtC,QAAK,IAAA,CAAA,QAAA,CAAS,mBAAmB,KAAc,CAAA,CAAA;AAC/C,QAAK,IAAA,CAAA,QAAA,CAAS,0BAA0B,KAAc,CAAA,CAAA;AAAA,OACxD;AAAA,KACF;AAAA,GACF,CAAA;AACF;;;;"}