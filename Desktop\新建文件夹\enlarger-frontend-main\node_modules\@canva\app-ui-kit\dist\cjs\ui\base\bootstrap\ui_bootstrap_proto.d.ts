import type { EnumUtil } from '../../../base/proto/proto';
export declare const enum Direction {
    LTR = 1,
    RTL = 2
}
export declare const DirectionUtil: EnumUtil<Direction>;
export declare const enum Theme {
    ADAPTIVE = 1,
    LIGHT = 2,
    DARK = 3
}
export declare const ThemeUtil: EnumUtil<Theme>;
export declare const enum AutoplayVideos {
    ADAPTIVE = 1,
    DONT_AUTOPLAY = 2,
    AUTOPLAY = 3
}
export declare const AutoplayVideosUtil: EnumUtil<AutoplayVideos>;
export interface UiBootstrap {
    readonly enableOverrides: boolean
    readonly enableAnimations: boolean
    readonly direction: Direction;
    readonly disableFocusTraps: boolean;
    readonly enableUserSelection: boolean;
    readonly theme: Theme | undefined
    readonly enableHighColorContrast: boolean | undefined;
    readonly autoplayVideos: AutoplayVideos | undefined
    readonly disableDialogBlur: boolean
    readonly exposeStringIds: boolean
}
export declare const UiBootstrap: {
    new (opts: {
        enableOverrides?: boolean
        enableAnimations: boolean
        direction: Direction;
        disableFocusTraps?: boolean;
        enableUserSelection?: boolean;
        theme?: Theme
        enableHighColorContrast?: boolean;
        autoplayVideos: AutoplayVideos
        disableDialogBlur?: boolean
        exposeStringIds?: boolean
    }): UiBootstrap;
    createUnchecked(opts: {
        enableOverrides?: boolean
        enableAnimations: boolean
        direction: Direction;
        disableFocusTraps?: boolean;
        enableUserSelection?: boolean;
        theme?: Theme
        enableHighColorContrast?: boolean;
        autoplayVideos?: AutoplayVideos
        disableDialogBlur?: boolean
        exposeStringIds?: boolean
    }): UiBootstrap;
    serialize(m: UiBootstrap): object;
    deserialize(o: object): UiBootstrap;
};
