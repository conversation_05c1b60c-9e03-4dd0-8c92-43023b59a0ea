Front-End Developer Interview Task: Mirror Flip Mini-Program

**Task Objective:** You are tasked with creating a mini-program on CANVA that can perform a mirror flip effect on an image. The goal is to demonstrate your skills in front-end development, particularly with regard to image manipulation, canvas operations, and integration into a web application. Your solution should be intuitive, user-friendly, and optimized for performance.

![img](https://lh7-rt.googleusercontent.com/docsz/AD_4nXc3C6Djc8VGgeaKjJfxlJLTktyAdkUD5H2ItjRoE75cdLzxVa47Q9AwPSMlQfsT--jvFIFT4asaLZV70LTn1g1DJ8VGlh6rvA0D5BXji3wdET7mdZZOlvQNQSf1O8wr0o44Z8W6PBvCEnS9tz6gluXyNqw?key=jcPlIYBxHmaypy7npFCghA)

**References:** To assist you in this task, we recommend reviewing the following resources:

- [Canva Developer Document Https://www.canva.dev/docs/apps/tashanDocumenhttps://www.canva.dev/docs/apps/tation](https://www.canva.dev/docs/apps/)
- [Canva's Easy Reflections App](https://www.canva.com/your-apps/AAF4vKXERBg/easy-reflections?q=easy+reflections)

**Task Instructions:**

**Part 1: Understanding the Application** Before starting, take some time to explore the Easy Reflections App.

- You need to replicate the following three features from this app:
  - **Position** (Below, Above, Left, Right)
  - **Offset** (Adjusting the distance)
  - **O**pacity (Adjusting the transparency)
- Focus on how the app implements the mirror flip feature and handles user interactions.

**Part 2: Initial Setup**

- Download the initial product architecture code provided.
  - https://drive.google.com/file/d/1tRoUkVA7eBUt0SW4oruZotUkmzERbHCB/view?usp=sharing
  - 
- Review the existing code and structure to understand the foundation on which you will build the mirror flip feature.

**Part 3: Implementation**

- Add a mirror flip feature to the mini-program on Canva. The mirror flip should:
  - **Horizontally flip an image**.
  - Provide a **preview** of the flipped image to the user.
  - Allow the user to **save or download** the flipped image.
- You are free to use any front-end technologies you are comfortable with (e.g., React, Vue, vanilla JavaScript), but keep in mind the principles of performance optimization and responsiveness.

**Requirements:**

- **Image Manipulation:**
  - The core functionality should include flipping an image horizontally (mirror effect).
- **User Interface:**
  - Design a minimal but functional interface where users can upload an image, view the flipped version, and save/download it.
- **Performance:**
  - The flipping operation should happen instantly, with no noticeable lag for the user.
  - Handle different image formats and sizes gracefully.
- **Responsiveness:**
  - The application should be responsive and work well on various screen sizes, including mobile and tablet.

**Bonus Points (Optional):**

- Implement additional options for vertical flipping.
- Add a slider to adjust the degree of reflection or transparency of the flipped image.

**Submission:**

- Submit your code as a GitHub repository link.
- Include a demo video and app link.
- Provide a README.md file with setup instructions and an explanation of your approach.

**Good Luck!** We are excited to see your approach.