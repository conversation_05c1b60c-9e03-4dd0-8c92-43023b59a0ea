import * as React from 'react';
import type { UnitSpace } from '../../../metrics/metrics';
import type { SurfaceHeaderProps } from '../../header/header';
import type { ContentContainerProps } from '../../internal/content_container';
import type { Offset as PinOffset, Placement as PinPlacement, ReferenceObject } from '../../pin/pin';
export type { ReferenceObject };
export type PopoverProps = {
    id?: string
    open: boolean
    blockOutsidePointerEvents?: boolean
    reference: React.ReactNode | ((triggerProps: TriggerProps) => React.ReactNode) | ReferenceObject
    placement?: Placement
    width?: Width
    widthMode?: WidthMode
    offset?: Offset
    arrow?: boolean
    children?: React.ReactNode
} & EventHandlerProps & HeaderAndFooterProps & ContentContainerProps;
type ExtractStrings<T> = T extends string ? T : never;
export type Placement = ExtractStrings<PinPlacement>;
export type TriggerProps = {
    ariaHasPopup?: PopoverProps['role']
    ariaControls?: string
    disclosure?: true
    pressed?: boolean
    active?: boolean
};
export type HeaderAndFooterProps = Pick<SurfaceHeaderProps, 'title' | 'description'> & {
    headerStart?: SurfaceHeaderProps['start']
    headerEnd?: SurfaceHeaderProps['end']
    headerDivider?: 'always' | 'when-scrolled' | 'never'
    headerAlignment?: SurfaceHeaderProps['alignment']
    header?: React.ReactNode
    footer?: React.ReactNode
};
type EventHandlerProps = {
    onRequestClose?: () => void
    onCloseComplete?: () => void
    onScroll?: (arg: {
        scrollTop: number;
    }) => void
};
export type Width = '16u' | '32u' | '40u' | '45u' | '52u' | 'reference' | 'auto';
export type WidthMode = 'fixed' | 'maximum';
export type StandardOffset = TypesafeExtract<UnitSpace, '0' | '0.5u' | '1u' | '2u' | '3u' | '4u' | '12u'>;
export type Offset = StandardOffset | PinOffset;
type TypesafeExtract<T, U extends T> = Extract<T, U>;
export declare const isReferenceObject: (reference: PopoverProps["reference"]) => reference is ReferenceObject;
export declare function Popover(
 { id: idProp, open, onRequestClose, onCloseComplete, onScroll, blockOutsidePointerEvents, reference, placement, width, widthMode, offset, arrow, children, title, description, header, headerStart, headerEnd, headerAlignment, headerDivider, footer, ...contentContainerProps }: PopoverProps
): import("react/jsx-runtime").JSX.Element;
