import * as React from 'react';
import type { TooltipProps, TriggerProps } from '../../../tooltip/tooltip';
export type ComponentAriaLabellingProps = {
    ariaLabel?: string
    ariaLabelledBy?: string
    ariaDescribedBy?: string
};
export type ComponentTooltipProps = {
    tooltipLabel?: TooltipProps['label']
    tooltipDescription?: TooltipProps['description']
    tooltipDisabled?: TooltipProps['disabled']
    tooltipShortcut?: TooltipProps['shortcut']
    tooltipPlacement?: TooltipProps['placement']
    tooltipLineClamp?: TooltipProps['lineClamp']
    tooltipCloseOnClick?: TooltipProps['closeOnClick']
};
type TooltipAriaProps = {
    tooltipId: TriggerProps['tooltipId']
    tooltipLabel?: ComponentTooltipProps['tooltipLabel']
    tooltipDescription?: ComponentTooltipProps['tooltipLabel']
};
export type TooltipTriggerEventHandlers = {
    onMouseDown?: React.MouseEventHandler
    onMouseEnter?: React.MouseEventHandler
    onMouseLeave?: React.MouseEventHandler
    onFocus?: React.FocusEventHandler
    onBlur?: React.FocusEventHandler
};
export declare function getComponentAriaAttributesConsideringTooltip(
 ariaAttrsProps: ComponentAriaLabellingProps & {
     children?: React.ReactNode;
 },
 tooltipProps: TooltipAriaProps
): ComponentAriaLabellingProps;
export type ComponentWithTooltip<T, R> = React.ForwardRefExoticComponent<React.PropsWithoutRef<T & ComponentTooltipProps> & React.RefAttributes<R | undefined>>;
export declare const withTooltip: <T extends TooltipTriggerEventHandlers & {
    children?: React.ReactNode;
}, R extends unknown>(Component: React.ComponentType<T>) => ComponentWithTooltip<T, R>;
export {};
