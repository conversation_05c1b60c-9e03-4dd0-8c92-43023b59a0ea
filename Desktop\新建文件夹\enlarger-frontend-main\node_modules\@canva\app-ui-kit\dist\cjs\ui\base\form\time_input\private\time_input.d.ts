import * as React from 'react';
import type { TimeZone } from '../../../date_time/utils/utils';
import type { BaseInputProps } from '../../base_input/base_input';
export type TimeInputProps = Omit<BaseInputProps, 'type' | 'value' | 'onChange' | 'placeholder' | 'min' | 'max' | 'end'> & {
    value?: number
    onChange?: (value: number | undefined) => void
    min?: number
    max?: number
    timezone?: TimeZone
    locale?: string
    end?: React.ReactNode
};
export declare const TimeInput: React.ForwardRefExoticComponent<Omit<BaseInputProps, "value" | "end" | "onChange" | "type" | "max" | "min" | "placeholder"> & {
    value?: number
    onChange?: (value: number | undefined) => void
    min?: number
    max?: number
    timezone?: TimeZone
    locale?: string
    end?: React.ReactNode
} & React.RefAttributes<HTMLInputElement>>;
