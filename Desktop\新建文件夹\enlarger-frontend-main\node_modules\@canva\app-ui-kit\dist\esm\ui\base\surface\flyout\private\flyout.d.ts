import * as React from 'react';
import type { SurfaceHeaderProps } from '../../header/header';
import type { ContentContainerProps } from '../../internal/content_container';
import type { Offset, Placement, ReferenceObject, Width as PopoverWidth, WidthMode } from '../../popover/popover';
export type HeaderAndFooterProps = Pick<SurfaceHeaderProps, 'title' | 'description'> & {
    headerStart?: Content
    headerEnd?: Content | 'none'
    headerDivider?: 'always' | 'when-scrolled' | 'never'
    headerAlignment?: SurfaceHeaderProps['alignment'] | 'auto'
    header?: Content
    footer?: Content
};
type EventHandlerProps = {
    onRequestClose?: () => void
    onCloseComplete?: () => void
};
export type FlyoutProps = {
    id?: string
    open: boolean
    blockOutsidePointerEvents?: boolean
    placement?: Placement
    offset?: Offset
    width?: Width
    dangerouslySetWidthMode?: WidthMode
    trigger: React.ReactNode | ((props: TriggerProps) => React.ReactNode) | ReferenceObject
    handleSafeAreaInsetBottom?: boolean
    children?: Content
} & ContentContainerProps & HeaderAndFooterProps & EventHandlerProps;
export type { Offset, Placement, WidthMode };
export type Width = 'trigger' | TypesafeExtract<PopoverWidth, '16u' | '32u' | '40u' | '45u' | '52u'>;
type TypesafeExtract<T, U extends T> = Extract<T, U>;
export type TriggerProps = {
    ariaHasPopup?: FlyoutProps['role']
    ariaControls?: string
    disclosure?: true
    pressed?: boolean
    active?: boolean
};
export type Content = React.ReactNode | ((props: FlyoutContentProps) => React.ReactNode);
export type FlyoutContentProps = {
    mode: Mode
};
export type Mode = (typeof modes)[number];
declare const modes: readonly ["sheet", "popover"];
export declare function Flyout(
 { open, onRequestClose, onCloseComplete, blockOutsidePointerEvents, trigger, width, dangerouslySetWidthMode, header, footer, children, title, description, headerStart, headerEnd, headerDivider, headerAlignment, ...contentContainerProps }: FlyoutProps
): import("react/jsx-runtime").JSX.Element;
