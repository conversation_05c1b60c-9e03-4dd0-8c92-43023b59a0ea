{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\nimport { NodePath, types as t } from '@babel/core'\n\nconst elements = ['svg', 'Svg']\n\ntype tag = 'title' | 'desc'\n\nexport interface Options {\n  tag: tag | null\n}\n\ninterface State {\n  opts: Options\n}\n\nconst createTagElement = (\n  tag: tag,\n  children: t.JSXExpressionContainer[] = [],\n  attributes: (t.JSXAttribute | t.JSXSpreadAttribute)[] = [],\n) => {\n  const eleName = t.jsxIdentifier(tag)\n  return t.jsxElement(\n    t.jsxOpeningElement(eleName, attributes),\n    t.jsxClosingElement(eleName),\n    children,\n  )\n}\n\nconst createTagIdAttribute = (tag: tag) =>\n  t.jsxAttribute(\n    t.jsxIdentifier('id'),\n    t.jsxExpressionContainer(t.identifier(`${tag}Id`)),\n  )\n\nconst addTagIdAttribute = (\n  tag: tag,\n  attributes: (t.JSXAttribute | t.JSXSpreadAttribute)[],\n) => {\n  const existingId = attributes.find(\n    (attribute) => t.isJSXAttribute(attribute) && attribute.name.name === 'id',\n  ) as t.JSXAttribute | undefined\n\n  if (!existingId) {\n    return [...attributes, createTagIdAttribute(tag)]\n  }\n  existingId.value = t.jsxExpressionContainer(\n    t.isStringLiteral(existingId.value)\n      ? t.logicalExpression('||', t.identifier(`${tag}Id`), existingId.value)\n      : t.identifier(`${tag}Id`),\n  )\n  return attributes\n}\n\nconst plugin = () => ({\n  visitor: {\n    JSXElement(path: NodePath<t.JSXElement>, state: State) {\n      const tag = state.opts.tag || 'title'\n      if (!elements.length) return\n\n      const openingElement = path.get('openingElement')\n      const openingElementName = openingElement.get('name')\n      if (\n        !elements.some((element) =>\n          openingElementName.isJSXIdentifier({ name: element }),\n        )\n      ) {\n        return\n      }\n\n      const getTagElement = (\n        existingTitle?: t.JSXElement,\n      ): t.JSXExpressionContainer => {\n        const tagExpression = t.identifier(tag)\n        if (existingTitle) {\n          existingTitle.openingElement.attributes = addTagIdAttribute(\n            tag,\n            existingTitle.openingElement.attributes,\n          )\n        }\n        const conditionalTitle = t.conditionalExpression(\n          tagExpression,\n          createTagElement(\n            tag,\n            [t.jsxExpressionContainer(tagExpression)],\n            existingTitle\n              ? existingTitle.openingElement.attributes\n              : [createTagIdAttribute(tag)],\n          ),\n          t.nullLiteral(),\n        )\n        if (existingTitle?.children?.length) {\n          // If title already exists render as follows\n          // `{title === undefined ? fallbackTitleElement : titleElement}`\n          return t.jsxExpressionContainer(\n            t.conditionalExpression(\n              t.binaryExpression(\n                '===',\n                tagExpression,\n                t.identifier('undefined'),\n              ),\n              existingTitle,\n              conditionalTitle,\n            ),\n          )\n        }\n        return t.jsxExpressionContainer(conditionalTitle)\n      }\n\n      // store the title element\n      let tagElement: t.JSXExpressionContainer | null = null\n\n      const hasTitle = path.get('children').some((childPath) => {\n        if (childPath.node === tagElement) return false\n        if (!childPath.isJSXElement()) return false\n        const name = childPath.get('openingElement').get('name')\n        if (!name.isJSXIdentifier()) return false\n        if (name.node.name !== tag) return false\n        tagElement = getTagElement(childPath.node)\n        childPath.replaceWith(tagElement)\n        return true\n      })\n\n      // create a title element if not already create\n      tagElement = tagElement || getTagElement()\n      if (!hasTitle) {\n        // path.unshiftContainer is not working well :(\n        // path.unshiftContainer('children', titleElement)\n        path.node.children.unshift(tagElement)\n        path.replaceWith(path.node)\n      }\n    },\n  },\n})\n\nexport default plugin\n"], "names": ["t"], "mappings": ";;;;AAGA,MAAM,QAAA,GAAW,CAAC,KAAA,EAAO,KAAK,CAAA,CAAA;AAY9B,MAAM,gBAAA,GAAmB,CACvB,GACA,EAAA,QAAA,GAAuC,EACvC,EAAA,UAAA,GAAwD,EACrD,KAAA;AACH,EAAM,MAAA,OAAA,GAAUA,UAAE,CAAA,aAAA,CAAc,GAAG,CAAA,CAAA;AACnC,EAAA,OAAOA,UAAE,CAAA,UAAA;AAAA,IACPA,UAAA,CAAE,iBAAkB,CAAA,OAAA,EAAS,UAAU,CAAA;AAAA,IACvCA,UAAA,CAAE,kBAAkB,OAAO,CAAA;AAAA,IAC3B,QAAA;AAAA,GACF,CAAA;AACF,CAAA,CAAA;AAEA,MAAM,oBAAA,GAAuB,CAAC,GAAA,KAC5BA,UAAE,CAAA,YAAA;AAAA,EACAA,UAAA,CAAE,cAAc,IAAI,CAAA;AAAA,EACpBA,WAAE,sBAAuB,CAAAA,UAAA,CAAE,UAAW,CAAA,CAAA,EAAG,OAAO,CAAC,CAAA;AACnD,CAAA,CAAA;AAEF,MAAM,iBAAA,GAAoB,CACxB,GAAA,EACA,UACG,KAAA;AACH,EAAA,MAAM,aAAa,UAAW,CAAA,IAAA;AAAA,IAC5B,CAAC,cAAcA,UAAE,CAAA,cAAA,CAAe,SAAS,CAAK,IAAA,SAAA,CAAU,KAAK,IAAS,KAAA,IAAA;AAAA,GACxE,CAAA;AAEA,EAAA,IAAI,CAAC,UAAY,EAAA;AACf,IAAA,OAAO,CAAC,GAAG,UAAY,EAAA,oBAAA,CAAqB,GAAG,CAAC,CAAA,CAAA;AAAA,GAClD;AACA,EAAA,UAAA,CAAW,QAAQA,UAAE,CAAA,sBAAA;AAAA,IACnBA,WAAE,eAAgB,CAAA,UAAA,CAAW,KAAK,CAC9B,GAAAA,UAAA,CAAE,kBAAkB,IAAM,EAAAA,UAAA,CAAE,WAAW,CAAG,EAAA,GAAA,CAAA,EAAA,CAAO,GAAG,UAAW,CAAA,KAAK,IACpEA,UAAE,CAAA,UAAA,CAAW,GAAG,GAAO,CAAA,EAAA,CAAA,CAAA;AAAA,GAC7B,CAAA;AACA,EAAO,OAAA,UAAA,CAAA;AACT,CAAA,CAAA;AAEA,MAAM,SAAS,OAAO;AAAA,EACpB,OAAS,EAAA;AAAA,IACP,UAAA,CAAW,MAA8B,KAAc,EAAA;AACrD,MAAM,MAAA,GAAA,GAAM,KAAM,CAAA,IAAA,CAAK,GAAO,IAAA,OAAA,CAAA;AAC9B,MAAA,IAAI,CAAC,QAAS,CAAA,MAAA;AAAQ,QAAA,OAAA;AAEtB,MAAM,MAAA,cAAA,GAAiB,IAAK,CAAA,GAAA,CAAI,gBAAgB,CAAA,CAAA;AAChD,MAAM,MAAA,kBAAA,GAAqB,cAAe,CAAA,GAAA,CAAI,MAAM,CAAA,CAAA;AACpD,MAAA,IACE,CAAC,QAAS,CAAA,IAAA;AAAA,QAAK,CAAC,OACd,KAAA,kBAAA,CAAmB,gBAAgB,EAAE,IAAA,EAAM,SAAS,CAAA;AAAA,OAEtD,EAAA;AACA,QAAA,OAAA;AAAA,OACF;AAEA,MAAM,MAAA,aAAA,GAAgB,CACpB,aAC6B,KAAA;AAvErC,QAAA,IAAA,EAAA,CAAA;AAwEQ,QAAM,MAAA,aAAA,GAAgBA,UAAE,CAAA,UAAA,CAAW,GAAG,CAAA,CAAA;AACtC,QAAA,IAAI,aAAe,EAAA;AACjB,UAAA,aAAA,CAAc,eAAe,UAAa,GAAA,iBAAA;AAAA,YACxC,GAAA;AAAA,YACA,cAAc,cAAe,CAAA,UAAA;AAAA,WAC/B,CAAA;AAAA,SACF;AACA,QAAA,MAAM,mBAAmBA,UAAE,CAAA,qBAAA;AAAA,UACzB,aAAA;AAAA,UACA,gBAAA;AAAA,YACE,GAAA;AAAA,YACA,CAACA,UAAA,CAAE,sBAAuB,CAAA,aAAa,CAAC,CAAA;AAAA,YACxC,gBACI,aAAc,CAAA,cAAA,CAAe,aAC7B,CAAC,oBAAA,CAAqB,GAAG,CAAC,CAAA;AAAA,WAChC;AAAA,UACAA,WAAE,WAAY,EAAA;AAAA,SAChB,CAAA;AACA,QAAI,IAAA,CAAA,EAAA,GAAA,aAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAe,QAAf,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAyB,MAAQ,EAAA;AAGnC,UAAA,OAAOA,UAAE,CAAA,sBAAA;AAAA,YACPA,UAAE,CAAA,qBAAA;AAAA,cACAA,UAAE,CAAA,gBAAA;AAAA,gBACA,KAAA;AAAA,gBACA,aAAA;AAAA,gBACAA,UAAA,CAAE,WAAW,WAAW,CAAA;AAAA,eAC1B;AAAA,cACA,aAAA;AAAA,cACA,gBAAA;AAAA,aACF;AAAA,WACF,CAAA;AAAA,SACF;AACA,QAAO,OAAAA,UAAA,CAAE,uBAAuB,gBAAgB,CAAA,CAAA;AAAA,OAClD,CAAA;AAGA,MAAA,IAAI,UAA8C,GAAA,IAAA,CAAA;AAElD,MAAA,MAAM,WAAW,IAAK,CAAA,GAAA,CAAI,UAAU,CAAE,CAAA,IAAA,CAAK,CAAC,SAAc,KAAA;AACxD,QAAA,IAAI,UAAU,IAAS,KAAA,UAAA;AAAY,UAAO,OAAA,KAAA,CAAA;AAC1C,QAAI,IAAA,CAAC,UAAU,YAAa,EAAA;AAAG,UAAO,OAAA,KAAA,CAAA;AACtC,QAAA,MAAM,OAAO,SAAU,CAAA,GAAA,CAAI,gBAAgB,CAAA,CAAE,IAAI,MAAM,CAAA,CAAA;AACvD,QAAI,IAAA,CAAC,KAAK,eAAgB,EAAA;AAAG,UAAO,OAAA,KAAA,CAAA;AACpC,QAAI,IAAA,IAAA,CAAK,KAAK,IAAS,KAAA,GAAA;AAAK,UAAO,OAAA,KAAA,CAAA;AACnC,QAAa,UAAA,GAAA,aAAA,CAAc,UAAU,IAAI,CAAA,CAAA;AACzC,QAAA,SAAA,CAAU,YAAY,UAAU,CAAA,CAAA;AAChC,QAAO,OAAA,IAAA,CAAA;AAAA,OACR,CAAA,CAAA;AAGD,MAAA,UAAA,GAAa,cAAc,aAAc,EAAA,CAAA;AACzC,MAAA,IAAI,CAAC,QAAU,EAAA;AAGb,QAAK,IAAA,CAAA,IAAA,CAAK,QAAS,CAAA,OAAA,CAAQ,UAAU,CAAA,CAAA;AACrC,QAAK,IAAA,CAAA,WAAA,CAAY,KAAK,IAAI,CAAA,CAAA;AAAA,OAC5B;AAAA,KACF;AAAA,GACF;AACF,CAAA;;;;"}