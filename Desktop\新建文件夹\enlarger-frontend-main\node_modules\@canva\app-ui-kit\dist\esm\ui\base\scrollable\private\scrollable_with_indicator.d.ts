import * as React from 'react';
import type { BackgroundLevel } from '../../theme/theme';
import type { BaseScrollableProps, ScrollableRef } from './base_scrollable';
type IndicatorType = 'shadow' | 'fade';
type ScrollableWithIndicatorProps = Pick<BaseScrollableProps, 'children' | 'onScroll' | 'direction' | 'ariaLabel' | 'role'> & {
    type: IndicatorType;
    background: BackgroundLevel;
};
export type Indicator = {
    type: ScrollableWithIndicatorProps['type']
    background?: ScrollableWithIndicatorProps['background']
};
export declare const ScrollableWithIndicator: React.ForwardRefExoticComponent<Pick<BaseScrollableProps, "children" | "direction" | "role" | "ariaLabel" | "onScroll"> & {
    type: IndicatorType;
    background: BackgroundLevel;
} & React.RefAttributes<ScrollableRef>>;
export {};
