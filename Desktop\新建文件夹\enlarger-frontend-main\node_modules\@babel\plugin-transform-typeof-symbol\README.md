# @babel/plugin-transform-typeof-symbol

> This transformer wraps all typeof expressions with a method that replicates native behaviour. (ie. returning “symbol” for symbols)

See our website [@babel/plugin-transform-typeof-symbol](https://babeljs.io/docs/babel-plugin-transform-typeof-symbol) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-typeof-symbol
```

or using yarn:

```sh
yarn add @babel/plugin-transform-typeof-symbol --dev
```
