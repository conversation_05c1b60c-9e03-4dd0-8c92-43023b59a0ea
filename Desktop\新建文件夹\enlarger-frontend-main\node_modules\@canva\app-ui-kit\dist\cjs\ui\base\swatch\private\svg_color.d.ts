import type { FillObject } from './swatch_util';
type ColorProps = {
    className?: string
    height: number
    width: number
    fill: string | undefined | FillObject
};
export declare const Color: ({ fill, ...props }: ColorProps) => import("react/jsx-runtime").JSX.Element;
type ColorStripesProps = {
    className?: string
    height: number
    width: number
    fills: readonly (string | undefined | FillObject)[]
};
export declare const ColorStripes: (({ className, height, width, fills }: ColorStripesProps) => import("react/jsx-runtime").JSX.Element) & {
    displayName: string;
};
export {};
