{"name": "@svgr/plugin-jsx", "description": "Transform SVG into JSX", "version": "8.1.0", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "repository": "https://github.com/gregberge/svgr/tree/main/packages/plugin-jsx", "author": "<PERSON> <<EMAIL>>", "publishConfig": {"access": "public"}, "keywords": ["svgr-plugin"], "engines": {"node": ">=14"}, "homepage": "https://react-svgr.com", "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}, "license": "MIT", "scripts": {"reset": "rm -rf dist", "build": "rollup -c ../../build/rollup.config.mjs", "prepublishOnly": "pnpm run reset && pnpm run build"}, "dependencies": {"@babel/core": "^7.21.3", "@svgr/babel-preset": "8.1.0", "@svgr/hast-util-to-babel-ast": "8.0.0", "svg-parser": "^2.0.4"}, "devDependencies": {"@svgr/core": "8.1.0", "@types/svg-parser": "^2.0.3"}, "peerDependencies": {"@svgr/core": "*"}, "gitHead": "772592e042be5063e782bfb8711d024b2fefc6b8"}