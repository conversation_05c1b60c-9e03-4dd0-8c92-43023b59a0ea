import * as React from 'react';
import type { Layout, ScrollState } from '../scroll_window';
type GlobalScrollWindowProps<L extends Layout> = {
    layout: L;
    children(opts: {
        scrollState: Readonly<ScrollState<L>>
    }): React.ReactNode;
};
export declare function GlobalScrollWindow<L extends Layout>(props: GlobalScrollWindowProps<L>): import("react/jsx-runtime").JSX.Element;
type OptionalGlobalScrollWindowProps<L extends Layout> = {
    layout: L;
    enabled: boolean;
    children(opts: {
        scrollState?: Readonly<ScrollState<L>>;
    }): React.ReactNode;
};
export declare function OptionalGlobalScrollWindow<L extends Layout>(props: OptionalGlobalScrollWindowProps<L>): string | number | boolean | Iterable<React.ReactNode> | import("react/jsx-runtime").JSX.Element | null | undefined;
export {};
