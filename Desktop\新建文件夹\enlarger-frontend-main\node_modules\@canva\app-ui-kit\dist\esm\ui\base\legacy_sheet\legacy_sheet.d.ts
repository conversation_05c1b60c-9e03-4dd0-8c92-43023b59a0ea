import * as React from 'react';
import type { BoundingRect } from 'react-measure';
import type { LayerLevel } from '../layer/layer';
import type { BackButtonEvent } from '../mobile_event_handler/mobile_event_handler';
import type { Role, Side } from './internal/layers';
import type { Opacity } from './internal/overlay';
import type { SheetBehaviorController, SheetState } from './internal/sheet_behavior';
export type { Side } from './internal/layers';
export type { SheetState } from './internal/sheet_behavior';
export type OnRequestCloseContext = {
    action: 'resize' | 'backdrop';
};
export declare const getDefaultSheetPeekHeight: (window: Pick<Window, "innerHeight">) => number;
export type SheetContent = React.ReactNode | (() => React.ReactNode);
export type { Opacity };
type BaseSheetProps = {
    id?: string;
    role?: Role
    ariaRoleDescription?: string
    content: SheetContent
    outerContent?: SheetContent
    placeholder?: SheetContent
    open?: boolean
    overlay?: boolean
    overlayOpacity?: Opacity
    backgroundColor?: string
    autoFocusOnContent?: boolean
    onBackButton?(event: BackButtonEvent): void
    onContentResize?(bounds: BoundingRect): void
    onRequestClose?(context?: OnRequestCloseContext): void
    onCloseAnimationComplete?(): void
    closeSheetText?: string
    mountBehaviour?: 'open' | 'lazy'
    onTopOfLayerChange?(onTop: boolean): void
    enableAnimations?: boolean
    sheetContainsTextInput?: boolean
};
export type LegacySheetProps = BaseSheetProps & {
    forceLayer?: boolean
    layerLevel?: LayerLevel
    from?: Side
    roundedCorners?: boolean
    expandContentIntoUnsafeArea?: boolean
    position?: 'top' | 'bottom'
    scrollable?: boolean
    size?: number | string
    maxSize?: number | string;
    minSize?: number | string;
    enableOverflow?: boolean
};
export declare const LegacySheet: (props: LegacySheetProps) => import("react/jsx-runtime").JSX.Element;
export type LegacyBottomSheetProps = BaseSheetProps & {
    onRequestClose?: (context?: OnRequestCloseContext) => void
    peekHeightPx?: 'none'
};
export declare const LegacyBottomSheet: ({ onRequestClose, onCloseAnimationComplete, peekHeightPx, ...restProps }: LegacyBottomSheetProps) => import("react/jsx-runtime").JSX.Element;
export declare const LegacyDeviceFrameSheet: (props: LegacySheetProps & {
    window: Window;
}) => import("react/jsx-runtime").JSX.Element;
export type SheetDraggable = {
    onPanning?: (isPanning: boolean) => void
    onStateChanged: (state: SheetState) => void
    peekSizePx?: number
    transparentOverlayOnPeek?: boolean
    viewportTopOffset?: number
    window: Window
};
export declare function createDraggableLegacyBottomSheet(
 { onStateChanged, onPanning, peekSizePx, transparentOverlayOnPeek, viewportTopOffset, window, }: SheetDraggable
): {
    Handle: React.FunctionComponent<{
        children?: React.ReactNode | undefined;
    }>;
    Sheet: (props: LegacySheetProps) => import("react/jsx-runtime").JSX.Element;
    sheetController: SheetBehaviorController;
    isSwipingSheet: () => boolean;
};
