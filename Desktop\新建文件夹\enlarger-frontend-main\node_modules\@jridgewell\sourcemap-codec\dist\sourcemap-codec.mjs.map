{"version": 3, "file": "sourcemap-codec.mjs", "sources": ["../src/sourcemap-codec.ts"], "sourcesContent": ["export type SourceMapSegment =\n  | [number]\n  | [number, number, number, number]\n  | [number, number, number, number, number];\nexport type SourceMapLine = SourceMapSegment[];\nexport type SourceMapMappings = SourceMapLine[];\n\nconst comma = ','.charCodeAt(0);\nconst semicolon = ';'.charCodeAt(0);\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nconst intToChar = new Uint8Array(64); // 64 possible chars.\nconst charToInt = new Uint8Array(128); // z is 122 in ASCII\n\nfor (let i = 0; i < chars.length; i++) {\n  const c = chars.charCodeAt(i);\n  intToChar[i] = c;\n  charToInt[c] = i;\n}\n\n// Provide a fallback for older environments.\nconst td =\n  typeof TextDecoder !== 'undefined'\n    ? /* #__PURE__ */ new TextDecoder()\n    : typeof Buffer !== 'undefined'\n    ? {\n        decode(buf: Uint8Array) {\n          const out = Buffer.from(buf.buffer, buf.byteOffset, buf.byteLength);\n          return out.toString();\n        },\n      }\n    : {\n        decode(buf: Uint8Array) {\n          let out = '';\n          for (let i = 0; i < buf.length; i++) {\n            out += String.fromCharCode(buf[i]);\n          }\n          return out;\n        },\n      };\n\nexport function decode(mappings: string): SourceMapMappings {\n  const state: [number, number, number, number, number] = new Int32Array(5) as any;\n  const decoded: SourceMapMappings = [];\n\n  let index = 0;\n  do {\n    const semi = indexOf(mappings, index);\n    const line: SourceMapLine = [];\n    let sorted = true;\n    let lastCol = 0;\n    state[0] = 0;\n\n    for (let i = index; i < semi; i++) {\n      let seg: SourceMapSegment;\n\n      i = decodeInteger(mappings, i, state, 0); // genColumn\n      const col = state[0];\n      if (col < lastCol) sorted = false;\n      lastCol = col;\n\n      if (hasMoreVlq(mappings, i, semi)) {\n        i = decodeInteger(mappings, i, state, 1); // sourcesIndex\n        i = decodeInteger(mappings, i, state, 2); // sourceLine\n        i = decodeInteger(mappings, i, state, 3); // sourceColumn\n\n        if (hasMoreVlq(mappings, i, semi)) {\n          i = decodeInteger(mappings, i, state, 4); // namesIndex\n          seg = [col, state[1], state[2], state[3], state[4]];\n        } else {\n          seg = [col, state[1], state[2], state[3]];\n        }\n      } else {\n        seg = [col];\n      }\n\n      line.push(seg);\n    }\n\n    if (!sorted) sort(line);\n    decoded.push(line);\n    index = semi + 1;\n  } while (index <= mappings.length);\n\n  return decoded;\n}\n\nfunction indexOf(mappings: string, index: number): number {\n  const idx = mappings.indexOf(';', index);\n  return idx === -1 ? mappings.length : idx;\n}\n\nfunction decodeInteger(mappings: string, pos: number, state: SourceMapSegment, j: number): number {\n  let value = 0;\n  let shift = 0;\n  let integer = 0;\n\n  do {\n    const c = mappings.charCodeAt(pos++);\n    integer = charToInt[c];\n    value |= (integer & 31) << shift;\n    shift += 5;\n  } while (integer & 32);\n\n  const shouldNegate = value & 1;\n  value >>>= 1;\n\n  if (shouldNegate) {\n    value = -0x80000000 | -value;\n  }\n\n  state[j] += value;\n  return pos;\n}\n\nfunction hasMoreVlq(mappings: string, i: number, length: number): boolean {\n  if (i >= length) return false;\n  return mappings.charCodeAt(i) !== comma;\n}\n\nfunction sort(line: SourceMapSegment[]) {\n  line.sort(sortComparator);\n}\n\nfunction sortComparator(a: SourceMapSegment, b: SourceMapSegment): number {\n  return a[0] - b[0];\n}\n\nexport function encode(decoded: SourceMapMappings): string;\nexport function encode(decoded: Readonly<SourceMapMappings>): string;\nexport function encode(decoded: Readonly<SourceMapMappings>): string {\n  const state: [number, number, number, number, number] = new Int32Array(5) as any;\n  const bufLength = 1024 * 16;\n  const subLength = bufLength - 36;\n  const buf = new Uint8Array(bufLength);\n  const sub = buf.subarray(0, subLength);\n  let pos = 0;\n  let out = '';\n\n  for (let i = 0; i < decoded.length; i++) {\n    const line = decoded[i];\n    if (i > 0) {\n      if (pos === bufLength) {\n        out += td.decode(buf);\n        pos = 0;\n      }\n      buf[pos++] = semicolon;\n    }\n    if (line.length === 0) continue;\n\n    state[0] = 0;\n\n    for (let j = 0; j < line.length; j++) {\n      const segment = line[j];\n      // We can push up to 5 ints, each int can take at most 7 chars, and we\n      // may push a comma.\n      if (pos > subLength) {\n        out += td.decode(sub);\n        buf.copyWithin(0, subLength, pos);\n        pos -= subLength;\n      }\n      if (j > 0) buf[pos++] = comma;\n\n      pos = encodeInteger(buf, pos, state, segment, 0); // genColumn\n\n      if (segment.length === 1) continue;\n      pos = encodeInteger(buf, pos, state, segment, 1); // sourcesIndex\n      pos = encodeInteger(buf, pos, state, segment, 2); // sourceLine\n      pos = encodeInteger(buf, pos, state, segment, 3); // sourceColumn\n\n      if (segment.length === 4) continue;\n      pos = encodeInteger(buf, pos, state, segment, 4); // namesIndex\n    }\n  }\n\n  return out + td.decode(buf.subarray(0, pos));\n}\n\nfunction encodeInteger(\n  buf: Uint8Array,\n  pos: number,\n  state: SourceMapSegment,\n  segment: SourceMapSegment,\n  j: number,\n): number {\n  const next = segment[j];\n  let num = next - state[j];\n  state[j] = next;\n\n  num = num < 0 ? (-num << 1) | 1 : num << 1;\n  do {\n    let clamped = num & 0b011111;\n    num >>>= 5;\n    if (num > 0) clamped |= 0b100000;\n    buf[pos++] = intToChar[clamped];\n  } while (num > 0);\n\n  return pos;\n}\n"], "names": [], "mappings": "AAOA,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACpC,MAAM,KAAK,GAAG,kEAAkE,CAAC;AACjF,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;AACrC,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACrC,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC9B,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CAClB;AAED;AACA,MAAM,EAAE,GACN,OAAO,WAAW,KAAK,WAAW;sBACd,IAAI,WAAW,EAAE;MACjC,OAAO,MAAM,KAAK,WAAW;UAC7B;YACE,MAAM,CAAC,GAAe;gBACpB,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;gBACpE,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;aACvB;SACF;UACD;YACE,MAAM,CAAC,GAAe;gBACpB,IAAI,GAAG,GAAG,EAAE,CAAC;gBACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACnC,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;iBACpC;gBACD,OAAO,GAAG,CAAC;aACZ;SACF,CAAC;SAEQ,MAAM,CAAC,QAAgB;IACrC,MAAM,KAAK,GAA6C,IAAI,UAAU,CAAC,CAAC,CAAQ,CAAC;IACjF,MAAM,OAAO,GAAsB,EAAE,CAAC;IAEtC,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,GAAG;QACD,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QACtC,MAAM,IAAI,GAAkB,EAAE,CAAC;QAC/B,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAEb,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YACjC,IAAI,GAAqB,CAAC;YAE1B,CAAC,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YACzC,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACrB,IAAI,GAAG,GAAG,OAAO;gBAAE,MAAM,GAAG,KAAK,CAAC;YAClC,OAAO,GAAG,GAAG,CAAC;YAEd,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE;gBACjC,CAAC,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;gBACzC,CAAC,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;gBACzC,CAAC,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;gBAEzC,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE;oBACjC,CAAC,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;oBACzC,GAAG,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBACrD;qBAAM;oBACL,GAAG,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC3C;aACF;iBAAM;gBACL,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;aACb;YAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAChB;QAED,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnB,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;KAClB,QAAQ,KAAK,IAAI,QAAQ,CAAC,MAAM,EAAE;IAEnC,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,OAAO,CAAC,QAAgB,EAAE,KAAa;IAC9C,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACzC,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC;AAC5C,CAAC;AAED,SAAS,aAAa,CAAC,QAAgB,EAAE,GAAW,EAAE,KAAuB,EAAE,CAAS;IACtF,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,OAAO,GAAG,CAAC,CAAC;IAEhB,GAAG;QACD,MAAM,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;QACrC,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QACvB,KAAK,IAAI,CAAC,OAAO,GAAG,EAAE,KAAK,KAAK,CAAC;QACjC,KAAK,IAAI,CAAC,CAAC;KACZ,QAAQ,OAAO,GAAG,EAAE,EAAE;IAEvB,MAAM,YAAY,GAAG,KAAK,GAAG,CAAC,CAAC;IAC/B,KAAK,MAAM,CAAC,CAAC;IAEb,IAAI,YAAY,EAAE;QAChB,KAAK,GAAG,CAAC,UAAU,GAAG,CAAC,KAAK,CAAC;KAC9B;IAED,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;IAClB,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,UAAU,CAAC,QAAgB,EAAE,CAAS,EAAE,MAAc;IAC7D,IAAI,CAAC,IAAI,MAAM;QAAE,OAAO,KAAK,CAAC;IAC9B,OAAO,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC;AAC1C,CAAC;AAED,SAAS,IAAI,CAAC,IAAwB;IACpC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,cAAc,CAAC,CAAmB,EAAE,CAAmB;IAC9D,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB,CAAC;SAIe,MAAM,CAAC,OAAoC;IACzD,MAAM,KAAK,GAA6C,IAAI,UAAU,CAAC,CAAC,CAAQ,CAAC;IACjF,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;IAC5B,MAAM,SAAS,GAAG,SAAS,GAAG,EAAE,CAAC;IACjC,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;IACtC,MAAM,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;IACvC,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,IAAI,GAAG,GAAG,EAAE,CAAC;IAEb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACvC,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,CAAC,GAAG,CAAC,EAAE;YACT,IAAI,GAAG,KAAK,SAAS,EAAE;gBACrB,GAAG,IAAI,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACtB,GAAG,GAAG,CAAC,CAAC;aACT;YACD,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC;SACxB;QACD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,SAAS;QAEhC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAEb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;;;YAGxB,IAAI,GAAG,GAAG,SAAS,EAAE;gBACnB,GAAG,IAAI,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACtB,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;gBAClC,GAAG,IAAI,SAAS,CAAC;aAClB;YACD,IAAI,CAAC,GAAG,CAAC;gBAAE,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC;YAE9B,GAAG,GAAG,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;YAEjD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAS;YACnC,GAAG,GAAG,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;YACjD,GAAG,GAAG,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;YACjD,GAAG,GAAG,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;YAEjD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAS;YACnC,GAAG,GAAG,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;SAClD;KACF;IAED,OAAO,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,aAAa,CACpB,GAAe,EACf,GAAW,EACX,KAAuB,EACvB,OAAyB,EACzB,CAAS;IAET,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IACxB,IAAI,GAAG,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1B,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAEhB,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;IAC3C,GAAG;QACD,IAAI,OAAO,GAAG,GAAG,GAAG,QAAQ,CAAC;QAC7B,GAAG,MAAM,CAAC,CAAC;QACX,IAAI,GAAG,GAAG,CAAC;YAAE,OAAO,IAAI,QAAQ,CAAC;QACjC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;KACjC,QAAQ,GAAG,GAAG,CAAC,EAAE;IAElB,OAAO,GAAG,CAAC;AACb;;;;"}