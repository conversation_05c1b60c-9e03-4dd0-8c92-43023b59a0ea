import type * as mobxUtils from 'mobx-utils';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './drag_handler';
export declare class MouseTracker {
    private readonly drag;
    private readonly onStopped;
    private readonly browserWindow;
    readonly type: "mouse";
    private simulateMoveSubscription?;
    constructor(drag: <PERSON><PERSON><PERSON><PERSON><PERSON>, simulateMove: mobxUtils.IObservableStream<unknown> | undefined, onStopped: () => void, browserWindow?: Window & typeof globalThis);
    stop(): void;
    private onMouseMove;
    private onMouseUp;
    private readonly onSimulateMove;
}
