import * as React from 'react';
import { LayerLevel } from '../../../layer/layer';
import type { Arrow, Boundary, BoundaryPadding, Offset, OnCalculateLayout, OnCalculateLayoutOptions, Placement as FixedPlacement, Rect, ReferenceObject } from './backend';
export type { FixedPlacement, ReferenceObject, Offset, Arrow, Rect, OnCalculateLayout, OnCalculateLayoutOptions, };
export type OnOutsidePointerDown = (option: OnOutsidePointerDownOptions) => void;
export type OnOutsidePointerDownOptions = {
    target: 'reference' | 'other';
};
export type PinProps = {
    open: boolean
    onOutsidePointerDown?: OnOutsidePointerDown
    blockInsidePointerEvents?: boolean
    blockOutsidePointerEvents?: boolean
    onCalculateLayout?: OnCalculateLayout
    reference: React.ReactNode | ReferenceObject
    placement?: Placement
    enableFlip?: boolean
    offset?: Offset
    boundary?: Boundary
    boundaryPadding?: BoundaryPadding
    level?: LayerLevel
    parentLayer?: HTMLElement
    children?: React.ReactNode | ((options: ChildrenOptions) => React.ReactNode)
};
export type ChildrenOptions = {
    setArrow: (arrow: Arrow | null) => void
};
export type Placement = 'auto' | FixedPlacement | PlacementConfig;
type PlacementConfig = {
    placement: FixedPlacement;
    rtlAware: boolean;
};
export type PinImperativeHandle = {
    update: () => void
};
export declare const Pin: React.ForwardRefExoticComponent<PinProps & React.RefAttributes<PinImperativeHandle>>;
