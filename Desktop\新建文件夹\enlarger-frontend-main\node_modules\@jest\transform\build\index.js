'use strict';

Object.defineProperty(exports, '__esModule', {
  value: true
});
Object.defineProperty(exports, 'createScriptTransformer', {
  enumerable: true,
  get: function () {
    return _ScriptTransformer.createScriptTransformer;
  }
});
Object.defineProperty(exports, 'createTranspilingRequire', {
  enumerable: true,
  get: function () {
    return _ScriptTransformer.createTranspilingRequire;
  }
});
Object.defineProperty(exports, 'handlePotentialSyntaxError', {
  enumerable: true,
  get: function () {
    return _enhanceUnexpectedTokenMessage.default;
  }
});
Object.defineProperty(exports, 'shouldInstrument', {
  enumerable: true,
  get: function () {
    return _shouldInstrument.default;
  }
});
var _ScriptTransformer = require('./ScriptTransformer');
var _shouldInstrument = _interopRequireDefault(require('./shouldInstrument'));
var _enhanceUnexpectedTokenMessage = _interopRequireDefault(
  require('./enhanceUnexpectedTokenMessage')
);
function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : {default: obj};
}
