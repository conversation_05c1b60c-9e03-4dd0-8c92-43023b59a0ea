{"version": 3, "file": "index.js", "sources": ["../src/state.ts", "../src/config.ts", "../src/plugins.ts", "../src/transform.ts"], "sourcesContent": ["import { parse as parsePath } from 'path'\n// @ts-ignore\nimport camelCase from 'camelcase'\nimport type { ConfigPlugin } from './plugins'\n\nexport interface State {\n  filePath?: string\n  componentName: string\n  caller?: {\n    name?: string\n    previousExport?: string | null\n    defaultPlugins?: ConfigPlugin[]\n  }\n}\n\nconst VALID_CHAR_REGEX = /[^a-zA-Z0-9 _-]/g\n\nconst getComponentName = (filePath?: string): string => {\n  if (!filePath) return 'SvgComponent'\n  const pascalCaseFileName = camelCase(\n    parsePath(filePath).name.replace(VALID_CHAR_REGEX, ''),\n    {\n      pascalCase: true,\n    },\n  )\n  return `Svg${pascalCaseFileName}`\n}\n\nexport const expandState = (state: Partial<State>): State => {\n  return {\n    componentName: state.componentName || getComponentName(state.filePath),\n    ...state,\n  }\n}\n", "import { cosmiconfig, cosmiconfigSync } from 'cosmiconfig'\nimport type { Options as PrettierOptions } from 'prettier'\nimport type { Config as SvgoConfig } from 'svgo'\nimport type { Options as TransformOptions } from '@svgr/babel-preset'\nimport type { TransformOptions as BabelTransformOptions } from '@babel/core'\nimport type { ConfigPlugin } from './plugins'\nimport type { State } from './state'\n\nexport interface Config {\n  ref?: boolean\n  titleProp?: boolean\n  descProp?: boolean\n  expandProps?: boolean | 'start' | 'end'\n  dimensions?: boolean\n  icon?: boolean | string | number\n  native?: boolean\n  svgProps?: {\n    [key: string]: string\n  }\n  replaceAttrValues?: {\n    [key: string]: string\n  }\n  runtimeConfig?: boolean\n  typescript?: boolean\n  prettier?: boolean\n  prettierConfig?: PrettierOptions\n  svgo?: boolean\n  svgoConfig?: SvgoConfig\n  configFile?: string\n  template?: TransformOptions['template']\n  memo?: boolean\n  exportType?: 'named' | 'default'\n  namedExport?: string\n  jsxRuntime?: 'classic' | 'classic-preact' | 'automatic'\n  jsxRuntimeImport?: {\n    source: string\n    namespace?: string\n    specifiers?: string[]\n    defaultSpecifier?: string\n  }\n\n  // CLI only\n  index?: boolean\n  plugins?: ConfigPlugin[]\n\n  // JSX\n  jsx?: {\n    babelConfig?: BabelTransformOptions\n  }\n}\n\nexport const DEFAULT_CONFIG: Config = {\n  dimensions: true,\n  expandProps: 'end',\n  icon: false,\n  native: false,\n  typescript: false,\n  prettier: true,\n  prettierConfig: undefined,\n  memo: false,\n  ref: false,\n  replaceAttrValues: undefined,\n  svgProps: undefined,\n  svgo: true,\n  svgoConfig: undefined,\n  template: undefined,\n  index: false,\n  titleProp: false,\n  descProp: false,\n  runtimeConfig: true,\n  namedExport: 'ReactComponent',\n  exportType: 'default',\n}\n\nconst explorer = cosmiconfig('svgr')\nconst explorerSync = cosmiconfigSync('svgr')\n\nexport const resolveConfig = async (\n  searchFrom?: string,\n  configFile?: string,\n): Promise<Config | null> => {\n  if (configFile == null) {\n    const result = await explorer.search(searchFrom)\n    return result ? result.config : null\n  }\n  const result = await explorer.load(configFile)\n  return result ? result.config : null\n}\n\nresolveConfig.sync = (\n  searchFrom?: string,\n  configFile?: string,\n): Config | null => {\n  if (configFile == null) {\n    const result = explorerSync.search(searchFrom)\n    return result ? result.config : null\n  }\n  const result = explorerSync.load(configFile)\n  return result ? result.config : null\n}\n\nexport const resolveConfigFile = async (\n  filePath: string,\n): Promise<string | null> => {\n  const result = await explorer.search(filePath)\n  return result ? result.filepath : null\n}\n\nresolveConfigFile.sync = (filePath: string): string | null => {\n  const result = explorerSync.search(filePath)\n  return result ? result.filepath : null\n}\n\nexport const loadConfig = async (\n  { configFile, ...baseConfig }: Config,\n  state: Pick<State, 'filePath'> = {},\n): Promise<Config> => {\n  const rcConfig =\n    state.filePath && baseConfig.runtimeConfig !== false\n      ? await resolveConfig(state.filePath, configFile)\n      : {}\n  return { ...DEFAULT_CONFIG, ...baseConfig, ...rcConfig }\n}\n\nloadConfig.sync = (\n  { configFile, ...baseConfig }: Config,\n  state: Pick<State, 'filePath'> = {},\n): Config => {\n  const rcConfig =\n    state.filePath && baseConfig.runtimeConfig !== false\n      ? resolveConfig.sync(state.filePath, configFile)\n      : {}\n  return { ...DEFAULT_CONFIG, ...baseConfig, ...rcConfig }\n}\n", "import { Config } from './config'\nimport type { State } from './state'\n\nexport interface Plugin {\n  (code: string, config: Config, state: State): string\n}\n\nexport type ConfigPlugin = string | Plugin\n\nconst DEFAULT_PLUGINS: Plugin[] = []\n\nexport const getPlugins = (\n  config: Config,\n  state: Partial<State>,\n): ConfigPlugin[] => {\n  if (config.plugins) {\n    return config.plugins\n  }\n\n  if (state.caller?.defaultPlugins) {\n    return state.caller.defaultPlugins\n  }\n\n  return DEFAULT_PLUGINS\n}\n\nexport const resolvePlugin = (plugin: ConfigPlugin): Plugin => {\n  if (typeof plugin === 'function') {\n    return plugin\n  }\n\n  if (typeof plugin === 'string') {\n    return loadPlugin(plugin)\n  }\n\n  throw new Error(`Invalid plugin \"${plugin}\"`)\n}\n\nconst pluginCache: Record<string, Plugin> = {}\n\nconst resolveModule = (m: any) => (m ? m.default || m : null)\n\nexport const loadPlugin = (moduleName: string): Plugin => {\n  if (pluginCache[moduleName]) {\n    return pluginCache[moduleName]\n  }\n\n  try {\n    // eslint-disable-next-line\n    const plugin = resolveModule(require(moduleName))\n    if (!plugin) {\n      throw new Error(`Invalid plugin \"${moduleName}\"`)\n    }\n    pluginCache[moduleName] = plugin\n    return pluginCache[moduleName]\n  } catch (error) {\n    console.log(error)\n    throw new Error(\n      `Module \"${moduleName}\" missing. Maybe \\`npm install ${moduleName}\\` could help!`,\n    )\n  }\n}\n", "import { expandState } from './state'\nimport { loadConfig } from './config'\nimport { resolvePlugin, getPlugins } from './plugins'\nimport type { Config } from './config'\nimport type { State } from './state'\n\nconst run = (code: string, config: Config, state: Partial<State>): string => {\n  const expandedState = expandState(state)\n  const plugins = getPlugins(config, state).map(resolvePlugin)\n  let nextCode = String(code).replace('\\0', '')\n  // eslint-disable-next-line no-restricted-syntax\n  for (const plugin of plugins) {\n    nextCode = plugin(nextCode, config, expandedState)\n  }\n  return nextCode\n}\n\nexport const transform = async (\n  code: string,\n  config: Config = {},\n  state: Partial<State> = {},\n): Promise<string> => {\n  config = await loadConfig(config, state)\n  return run(code, config, state)\n}\n\ntransform.sync = (\n  code: string,\n  config: Config = {},\n  state: Partial<State> = {},\n): string => {\n  config = loadConfig.sync(config, state)\n  return run(code, config, state)\n}\n"], "names": ["parsePath", "__spreadValues", "cosmiconfig", "cosmiconfigSync", "result"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAeA,MAAM,gBAAmB,GAAA,kBAAA,CAAA;AAEzB,MAAM,gBAAA,GAAmB,CAAC,QAA8B,KAAA;AACtD,EAAA,IAAI,CAAC,QAAA;AAAU,IAAO,OAAA,cAAA,CAAA;AACtB,EAAA,MAAM,kBAAqB,GAAA,SAAA;AAAA,IACzBA,WAAU,QAAQ,CAAA,CAAE,IAAK,CAAA,OAAA,CAAQ,kBAAkB,EAAE,CAAA;AAAA,IACrD;AAAA,MACE,UAAY,EAAA,IAAA;AAAA,KACd;AAAA,GACF,CAAA;AACA,EAAA,OAAO,CAAM,GAAA,EAAA,kBAAA,CAAA,CAAA,CAAA;AACf,CAAA,CAAA;AAEa,MAAA,WAAA,GAAc,CAAC,KAAiC,KAAA;AAC3D,EAAO,OAAAC,gBAAA,CAAA;AAAA,IACL,aAAe,EAAA,KAAA,CAAM,aAAiB,IAAA,gBAAA,CAAiB,MAAM,QAAQ,CAAA;AAAA,GAClE,EAAA,KAAA,CAAA,CAAA;AAEP,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkBO,MAAM,cAAyB,GAAA;AAAA,EACpC,UAAY,EAAA,IAAA;AAAA,EACZ,WAAa,EAAA,KAAA;AAAA,EACb,IAAM,EAAA,KAAA;AAAA,EACN,MAAQ,EAAA,KAAA;AAAA,EACR,UAAY,EAAA,KAAA;AAAA,EACZ,QAAU,EAAA,IAAA;AAAA,EACV,cAAgB,EAAA,KAAA,CAAA;AAAA,EAChB,IAAM,EAAA,KAAA;AAAA,EACN,GAAK,EAAA,KAAA;AAAA,EACL,iBAAmB,EAAA,KAAA,CAAA;AAAA,EACnB,QAAU,EAAA,KAAA,CAAA;AAAA,EACV,IAAM,EAAA,IAAA;AAAA,EACN,UAAY,EAAA,KAAA,CAAA;AAAA,EACZ,QAAU,EAAA,KAAA,CAAA;AAAA,EACV,KAAO,EAAA,KAAA;AAAA,EACP,SAAW,EAAA,KAAA;AAAA,EACX,QAAU,EAAA,KAAA;AAAA,EACV,aAAe,EAAA,IAAA;AAAA,EACf,WAAa,EAAA,gBAAA;AAAA,EACb,UAAY,EAAA,SAAA;AACd,EAAA;AAEA,MAAM,QAAA,GAAWC,wBAAY,MAAM,CAAA,CAAA;AACnC,MAAM,YAAA,GAAeC,4BAAgB,MAAM,CAAA,CAAA;AAE9B,MAAA,aAAA,GAAgB,OAC3B,UAAA,EACA,UAC2B,KAAA;AAC3B,EAAA,IAAI,cAAc,IAAM,EAAA;AACtB,IAAA,MAAMC,OAAS,GAAA,MAAM,QAAS,CAAA,MAAA,CAAO,UAAU,CAAA,CAAA;AAC/C,IAAOA,OAAAA,OAAAA,GAASA,QAAO,MAAS,GAAA,IAAA,CAAA;AAAA,GAClC;AACA,EAAA,MAAM,MAAS,GAAA,MAAM,QAAS,CAAA,IAAA,CAAK,UAAU,CAAA,CAAA;AAC7C,EAAO,OAAA,MAAA,GAAS,OAAO,MAAS,GAAA,IAAA,CAAA;AAClC,EAAA;AAEA,aAAc,CAAA,IAAA,GAAO,CACnB,UAAA,EACA,UACkB,KAAA;AAClB,EAAA,IAAI,cAAc,IAAM,EAAA;AACtB,IAAMA,MAAAA,OAAAA,GAAS,YAAa,CAAA,MAAA,CAAO,UAAU,CAAA,CAAA;AAC7C,IAAOA,OAAAA,OAAAA,GAASA,QAAO,MAAS,GAAA,IAAA,CAAA;AAAA,GAClC;AACA,EAAM,MAAA,MAAA,GAAS,YAAa,CAAA,IAAA,CAAK,UAAU,CAAA,CAAA;AAC3C,EAAO,OAAA,MAAA,GAAS,OAAO,MAAS,GAAA,IAAA,CAAA;AAClC,CAAA,CAAA;AAEa,MAAA,iBAAA,GAAoB,OAC/B,QAC2B,KAAA;AAC3B,EAAA,MAAM,MAAS,GAAA,MAAM,QAAS,CAAA,MAAA,CAAO,QAAQ,CAAA,CAAA;AAC7C,EAAO,OAAA,MAAA,GAAS,OAAO,QAAW,GAAA,IAAA,CAAA;AACpC,EAAA;AAEA,iBAAkB,CAAA,IAAA,GAAO,CAAC,QAAoC,KAAA;AAC5D,EAAM,MAAA,MAAA,GAAS,YAAa,CAAA,MAAA,CAAO,QAAQ,CAAA,CAAA;AAC3C,EAAO,OAAA,MAAA,GAAS,OAAO,QAAW,GAAA,IAAA,CAAA;AACpC,CAAA,CAAA;AAEO,MAAM,UAAa,GAAA,OACxB,EACA,EAAA,KAAA,GAAiC,EACb,KAAA;AAFpB,EAAA,IAAA,EAAA,GAAA,EAAA,EAAE,EAlHJ,UAAA,EAAA,GAkHE,EAAiB,EAAA,UAAA,GAAA,SAAA,CAAjB,IAAiB,CAAf,YAAA,CAAA,CAAA,CAAA;AAGF,EAAA,MAAM,QACJ,GAAA,KAAA,CAAM,QAAY,IAAA,UAAA,CAAW,aAAkB,KAAA,KAAA,GAC3C,MAAM,aAAA,CAAc,KAAM,CAAA,QAAA,EAAU,UAAU,CAAA,GAC9C,EAAC,CAAA;AACP,EAAO,OAAA,cAAA,CAAA,cAAA,CAAA,cAAA,CAAA,EAAA,EAAK,iBAAmB,UAAe,CAAA,EAAA,QAAA,CAAA,CAAA;AAChD,EAAA;AAEA,UAAA,CAAW,IAAO,GAAA,CAChB,EACA,EAAA,KAAA,GAAiC,EACtB,KAAA;AAFX,EAAA,IAAA,EAAA,GAAA,EAAA,EAAE,EA7HJ,UAAA,EAAA,GA6HE,EAAiB,EAAA,UAAA,GAAA,SAAA,CAAjB,IAAiB,CAAf,YAAA,CAAA,CAAA,CAAA;AAGF,EAAA,MAAM,QACJ,GAAA,KAAA,CAAM,QAAY,IAAA,UAAA,CAAW,aAAkB,KAAA,KAAA,GAC3C,aAAc,CAAA,IAAA,CAAK,KAAM,CAAA,QAAA,EAAU,UAAU,CAAA,GAC7C,EAAC,CAAA;AACP,EAAO,OAAA,cAAA,CAAA,cAAA,CAAA,cAAA,CAAA,EAAA,EAAK,iBAAmB,UAAe,CAAA,EAAA,QAAA,CAAA,CAAA;AAChD,CAAA;;AC5HA,MAAM,kBAA4B,EAAC,CAAA;AAEtB,MAAA,UAAA,GAAa,CACxB,MAAA,EACA,KACmB,KAAA;AAdrB,EAAA,IAAA,EAAA,CAAA;AAeE,EAAA,IAAI,OAAO,OAAS,EAAA;AAClB,IAAA,OAAO,MAAO,CAAA,OAAA,CAAA;AAAA,GAChB;AAEA,EAAI,IAAA,CAAA,EAAA,GAAA,KAAA,CAAM,MAAN,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAc,cAAgB,EAAA;AAChC,IAAA,OAAO,MAAM,MAAO,CAAA,cAAA,CAAA;AAAA,GACtB;AAEA,EAAO,OAAA,eAAA,CAAA;AACT,CAAA,CAAA;AAEa,MAAA,aAAA,GAAgB,CAAC,MAAiC,KAAA;AAC7D,EAAI,IAAA,OAAO,WAAW,UAAY,EAAA;AAChC,IAAO,OAAA,MAAA,CAAA;AAAA,GACT;AAEA,EAAI,IAAA,OAAO,WAAW,QAAU,EAAA;AAC9B,IAAA,OAAO,WAAW,MAAM,CAAA,CAAA;AAAA,GAC1B;AAEA,EAAM,MAAA,IAAI,KAAM,CAAA,CAAA,gBAAA,EAAmB,MAAS,CAAA,CAAA,CAAA,CAAA,CAAA;AAC9C,CAAA,CAAA;AAEA,MAAM,cAAsC,EAAC,CAAA;AAE7C,MAAM,gBAAgB,CAAC,CAAA,KAAY,CAAI,GAAA,CAAA,CAAE,WAAW,CAAI,GAAA,IAAA,CAAA;AAE3C,MAAA,UAAA,GAAa,CAAC,UAA+B,KAAA;AACxD,EAAI,IAAA,WAAA,CAAY,UAAU,CAAG,EAAA;AAC3B,IAAA,OAAO,YAAY,UAAU,CAAA,CAAA;AAAA,GAC/B;AAEA,EAAI,IAAA;AAEF,IAAA,MAAM,MAAS,GAAA,aAAA,CAAc,OAAQ,CAAA,UAAU,CAAC,CAAA,CAAA;AAChD,IAAA,IAAI,CAAC,MAAQ,EAAA;AACX,MAAM,MAAA,IAAI,KAAM,CAAA,CAAA,gBAAA,EAAmB,UAAa,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,KAClD;AACA,IAAA,WAAA,CAAY,UAAU,CAAI,GAAA,MAAA,CAAA;AAC1B,IAAA,OAAO,YAAY,UAAU,CAAA,CAAA;AAAA,WACtB,KAAP,EAAA;AACA,IAAA,OAAA,CAAQ,IAAI,KAAK,CAAA,CAAA;AACjB,IAAA,MAAM,IAAI,KAAA;AAAA,MACR,WAAW,UAA4C,CAAA,+BAAA,EAAA,UAAA,CAAA,cAAA,CAAA;AAAA,KACzD,CAAA;AAAA,GACF;AACF,CAAA;;ACvDA,MAAM,GAAM,GAAA,CAAC,IAAc,EAAA,MAAA,EAAgB,KAAkC,KAAA;AAC3E,EAAM,MAAA,aAAA,GAAgB,YAAY,KAAK,CAAA,CAAA;AACvC,EAAA,MAAM,UAAU,UAAW,CAAA,MAAA,EAAQ,KAAK,CAAA,CAAE,IAAI,aAAa,CAAA,CAAA;AAC3D,EAAA,IAAI,WAAW,MAAO,CAAA,IAAI,CAAE,CAAA,OAAA,CAAQ,MAAM,EAAE,CAAA,CAAA;AAE5C,EAAA,KAAA,MAAW,UAAU,OAAS,EAAA;AAC5B,IAAW,QAAA,GAAA,MAAA,CAAO,QAAU,EAAA,MAAA,EAAQ,aAAa,CAAA,CAAA;AAAA,GACnD;AACA,EAAO,OAAA,QAAA,CAAA;AACT,CAAA,CAAA;AAEa,MAAA,SAAA,GAAY,OACvB,IACA,EAAA,MAAA,GAAiB,EACjB,EAAA,KAAA,GAAwB,EACJ,KAAA;AACpB,EAAS,MAAA,GAAA,MAAM,UAAW,CAAA,MAAA,EAAQ,KAAK,CAAA,CAAA;AACvC,EAAO,OAAA,GAAA,CAAI,IAAM,EAAA,MAAA,EAAQ,KAAK,CAAA,CAAA;AAChC,EAAA;AAEA,SAAU,CAAA,IAAA,GAAO,CACf,IACA,EAAA,MAAA,GAAiB,EACjB,EAAA,KAAA,GAAwB,EACb,KAAA;AACX,EAAS,MAAA,GAAA,UAAA,CAAW,IAAK,CAAA,MAAA,EAAQ,KAAK,CAAA,CAAA;AACtC,EAAO,OAAA,GAAA,CAAI,IAAM,EAAA,MAAA,EAAQ,KAAK,CAAA,CAAA;AAChC,CAAA;;;;;;;;"}