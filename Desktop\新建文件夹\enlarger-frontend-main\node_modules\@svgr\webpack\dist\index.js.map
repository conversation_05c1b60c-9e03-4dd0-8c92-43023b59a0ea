{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["import { callbackify } from 'util'\nimport { transformAsync, createConfigItem } from '@babel/core'\nimport { transform, Config, State } from '@svgr/core'\nimport { normalize } from 'path'\nimport svgo from '@svgr/plugin-svgo'\nimport jsx from '@svgr/plugin-jsx'\n// @ts-ignore\nimport presetReact from '@babel/preset-react'\n// @ts-ignore\nimport presetEnv from '@babel/preset-env'\n// @ts-ignore\nimport presetTS from '@babel/preset-typescript'\n// @ts-ignore\nimport pluginTransformReactConstantElements from '@babel/plugin-transform-react-constant-elements'\nimport type * as webpack from 'webpack'\n\nconst babelOptions = {\n  babelrc: false,\n  configFile: false,\n  presets: [\n    createConfigItem(presetReact, { type: 'preset' }),\n    createConfigItem([presetEnv, { modules: false }], { type: 'preset' }),\n  ],\n  plugins: [createConfigItem(pluginTransformReactConstantElements)],\n}\n\nconst typeScriptBabelOptions = {\n  ...babelOptions,\n  presets: [\n    ...babelOptions.presets,\n    createConfigItem(\n      [presetTS, { allowNamespaces: true, allExtensions: true, isTSX: true }],\n      { type: 'preset' },\n    ),\n  ],\n}\n\ninterface LoaderOptions extends Config {\n  babel?: boolean\n}\n\nconst tranformSvg = callbackify(\n  async (contents: string, options: LoaderOptions, state: Partial<State>) => {\n    const { babel = true, ...config } = options\n    const jsCode = await transform(contents, config, state)\n    if (!babel) return jsCode\n    const result = await transformAsync(\n      jsCode,\n      options.typescript ? typeScriptBabelOptions : babelOptions,\n    )\n    if (!result?.code) {\n      throw new Error(`Error while transforming using Babel`)\n    }\n    return result.code\n  },\n)\n\nfunction svgrLoader(\n  this: webpack.LoaderContext<LoaderOptions>,\n  contents: string,\n): void {\n  this.cacheable && this.cacheable()\n  const callback = this.async()\n\n  const options = this.getOptions()\n\n  const previousExport = (() => {\n    if (contents.startsWith('export ')) return contents\n    const exportMatches = contents.match(/^module.exports\\s*=\\s*(.*)/)\n    return exportMatches ? `export default ${exportMatches[1]}` : null\n  })()\n\n  const state = {\n    caller: {\n      name: '@svgr/webpack',\n      previousExport,\n      defaultPlugins: [svgo, jsx],\n    },\n    filePath: normalize(this.resourcePath),\n  }\n\n  if (!previousExport) {\n    tranformSvg(contents, options, state, callback)\n  } else {\n    this.fs.readFile(this.resourcePath, (err, result) => {\n      if (err) {\n        callback(err)\n        return\n      }\n      tranformSvg(String(result), options, state, (err, content) => {\n        if (err) {\n          callback(err)\n          return\n        }\n        callback(null, content)\n      })\n    })\n  }\n}\n\nexport default svgrLoader\n"], "names": ["createConfigItem", "callbackify", "transform", "transformAsync", "normalize", "err"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,MAAM,YAAe,GAAA;AAAA,EACnB,OAAS,EAAA,KAAA;AAAA,EACT,UAAY,EAAA,KAAA;AAAA,EACZ,OAAS,EAAA;AAAA,IACPA,qBAAiB,CAAA,WAAA,EAAa,EAAE,IAAA,EAAM,UAAU,CAAA;AAAA,IAChDA,qBAAA,CAAiB,CAAC,SAAA,EAAW,EAAE,OAAA,EAAS,KAAM,EAAC,CAAG,EAAA,EAAE,IAAM,EAAA,QAAA,EAAU,CAAA;AAAA,GACtE;AAAA,EACA,OAAS,EAAA,CAACA,qBAAiB,CAAA,oCAAoC,CAAC,CAAA;AAClE,CAAA,CAAA;AAEA,MAAM,sBAAA,GAAyB,iCAC1B,YAD0B,CAAA,EAAA;AAAA,EAE7B,OAAS,EAAA;AAAA,IACP,GAAG,YAAa,CAAA,OAAA;AAAA,IAChBA,qBAAA;AAAA,MACE,CAAC,UAAU,EAAE,eAAA,EAAiB,MAAM,aAAe,EAAA,IAAA,EAAM,KAAO,EAAA,IAAA,EAAM,CAAA;AAAA,MACtE,EAAE,MAAM,QAAS,EAAA;AAAA,KACnB;AAAA,GACF;AACF,CAAA,CAAA,CAAA;AAMA,MAAM,WAAc,GAAAC,gBAAA;AAAA,EAClB,OAAO,QAAkB,EAAA,OAAA,EAAwB,KAA0B,KAAA;AACzE,IAAA,MAAoC,cAA5B,EAAQ,KAAA,GAAA,IAAA,KAAoB,EAAX,EAAA,MAAA,GAAA,SAAA,CAAW,IAAX,CAAjB,OAAA,CAAA,CAAA,CAAA;AACR,IAAA,MAAM,MAAS,GAAA,MAAMC,gBAAU,CAAA,QAAA,EAAU,QAAQ,KAAK,CAAA,CAAA;AACtD,IAAA,IAAI,CAAC,KAAA;AAAO,MAAO,OAAA,MAAA,CAAA;AACnB,IAAA,MAAM,SAAS,MAAMC,mBAAA;AAAA,MACnB,MAAA;AAAA,MACA,OAAA,CAAQ,aAAa,sBAAyB,GAAA,YAAA;AAAA,KAChD,CAAA;AACA,IAAI,IAAA,EAAC,iCAAQ,IAAM,CAAA,EAAA;AACjB,MAAM,MAAA,IAAI,MAAM,CAAsC,oCAAA,CAAA,CAAA,CAAA;AAAA,KACxD;AACA,IAAA,OAAO,MAAO,CAAA,IAAA,CAAA;AAAA,GAChB;AACF,CAAA,CAAA;AAEA,SAAS,WAEP,QACM,EAAA;AACN,EAAK,IAAA,CAAA,SAAA,IAAa,KAAK,SAAU,EAAA,CAAA;AACjC,EAAM,MAAA,QAAA,GAAW,KAAK,KAAM,EAAA,CAAA;AAE5B,EAAM,MAAA,OAAA,GAAU,KAAK,UAAW,EAAA,CAAA;AAEhC,EAAA,MAAM,kBAAkB,MAAM;AAC5B,IAAI,IAAA,QAAA,CAAS,WAAW,SAAS,CAAA;AAAG,MAAO,OAAA,QAAA,CAAA;AAC3C,IAAM,MAAA,aAAA,GAAgB,QAAS,CAAA,KAAA,CAAM,4BAA4B,CAAA,CAAA;AACjE,IAAA,OAAO,aAAgB,GAAA,CAAA,eAAA,EAAkB,aAAc,CAAA,CAAC,CAAM,CAAA,CAAA,GAAA,IAAA,CAAA;AAAA,GAC7D,GAAA,CAAA;AAEH,EAAA,MAAM,KAAQ,GAAA;AAAA,IACZ,MAAQ,EAAA;AAAA,MACN,IAAM,EAAA,eAAA;AAAA,MACN,cAAA;AAAA,MACA,cAAA,EAAgB,CAAC,IAAA,EAAM,GAAG,CAAA;AAAA,KAC5B;AAAA,IACA,QAAA,EAAUC,cAAU,CAAA,IAAA,CAAK,YAAY,CAAA;AAAA,GACvC,CAAA;AAEA,EAAA,IAAI,CAAC,cAAgB,EAAA;AACnB,IAAY,WAAA,CAAA,QAAA,EAAU,OAAS,EAAA,KAAA,EAAO,QAAQ,CAAA,CAAA;AAAA,GACzC,MAAA;AACL,IAAA,IAAA,CAAK,GAAG,QAAS,CAAA,IAAA,CAAK,YAAc,EAAA,CAAC,KAAK,MAAW,KAAA;AACnD,MAAA,IAAI,GAAK,EAAA;AACP,QAAA,QAAA,CAAS,GAAG,CAAA,CAAA;AACZ,QAAA,OAAA;AAAA,OACF;AACA,MAAA,WAAA,CAAY,OAAO,MAAM,CAAA,EAAG,SAAS,KAAO,EAAA,CAACC,MAAK,OAAY,KAAA;AAC5D,QAAA,IAAIA,IAAK,EAAA;AACP,UAAA,QAAA,CAASA,IAAG,CAAA,CAAA;AACZ,UAAA,OAAA;AAAA,SACF;AACA,QAAA,QAAA,CAAS,MAAM,OAAO,CAAA,CAAA;AAAA,OACvB,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAAA,GACH;AACF;;;;"}