import * as React from 'react';
import type { ButtonProps } from '../../button/button';
import type { Space } from '../../metrics/metrics';
export type MenuVariant = 'regular' | 'rounded';
export type MenuSpacing = Extract<Space, '0' | '0.5u' | '1u'>;
export type MenuRole = 'list' | 'menu' | 'menubar' | 'navigation' | 'listbox' | 'group';
export type MenuDirection = 'vertical' | 'horizontal';
export type MenuContextType = {
    menuRole: MenuRole;
    menuDirection?: MenuDirection;
    menuVariant?: MenuVariant;
    menuSpacing?: MenuSpacing;
    registerToggleMenu: () => void;
} | undefined;
export declare const ITEM_HEIGHT_MULTIPIER = 5;
export declare const DIVIDER_HEIGHT_MULTIPIER = 2;
export declare const TEXT_DIVIDER_HEIGHT_MULTIPIER = 4;
export type MenuProps = {
    children?: React.ReactNode
    id?: string
    className?: string
    variant?: MenuVariant
    spacing?: MenuSpacing
} & A11yProps & BleedProps & DirectionProps;
type A11yProps = {
    role: MenuRole
    tagName?: 'ul' | 'ol'
    ariaActiveDescendant?: string
    ariaMultiSelectable?: boolean
    ariaLabel?: string
    ariaLabelledBy?: string
};
type BleedProps = {
    variant: Extract<MenuVariant, 'rounded'>;
    direction?: 'vertical';
    bleedX: boolean
} | {
    bleedX?: false;
};
type DirectionProps = {
    role: Exclude<MenuRole, 'listbox'>;
    direction?: 'vertical' | 'horizontal'
} | {
    direction?: never;
};
export declare const Menu: React.ForwardRefExoticComponent<MenuProps & React.RefAttributes<HTMLElement>>;
export type MenuItemProps = {
    id?: string
    buttonId?: string
} & MenuItemButtonSharedProps;
type Decorator = (() => React.ReactNode) | React.ReactNode;
export type MenuItemButtonSharedProps = {
    children?: React.ReactNode
    className?: string
    href?: string
    target?: string
    onClick?: (event: React.MouseEvent<any>) => void
    onContextMenu?: (event: React.MouseEvent<any>) => void
    start?: Decorator
    end?: Decorator
    label?: string
    description?: string
    lineClamp?: number
    active?: boolean
    selected?: boolean
    pressed?: boolean
    disableActiveStyle?: boolean
    disabled?: boolean
    onFocus?: React.FocusEventHandler
    onBlur?: React.FocusEventHandler
    onMouseEnter?: (event: React.MouseEvent<any>) => void
    onMouseLeave?: (event: React.MouseEvent<any>) => void
    buttonRef?: React.Ref<HTMLElement>
    ariaSelected?: boolean
} & Pick<ButtonProps, 'ariaLabel' | 'ariaLabelledBy' | 'ariaDescribedBy' | 'ariaControls' | 'disclosure' | 'draggable' | 'ariaHasPopup' | 'ariaCurrent' | 'tabIndex' | 'tooltipLabel' | 'tooltipDisabled' | 'tooltipShortcut' | 'tooltipPlacement' | 'tooltipLineClamp'>;
export type MenuItemButtonProps = MenuItemButtonSharedProps & {
    id?: string
    alignment?: 'start' | 'center'
} & Pick<ButtonProps, 'role'>;
export declare const MenuItem: React.NamedExoticComponent<MenuItemProps>;
export declare const MenuItemButton: React.FunctionComponent<MenuItemButtonProps>;
export declare const renderDecorator: (decorator?: React.ReactNode) => React.JSX.Element;
export declare const MenuDivider: React.NamedExoticComponent<{
    id?: string
    children?: React.ReactNode
}>;
export type ToggleMenuProps = MenuProps & {
    id?: string;
    menuId?: string
    labelId?: string
    label: string
    labelNode?: React.ReactNode
    href?: string
    onToggleClick?: () => void
    onClick?: (event: React.MouseEvent<any>) => void
    onContextMenu?: (event: React.MouseEvent<any>) => void
    toggleLabel?: string
    defaultExpanded?: boolean
    expanded?: boolean
    buttonClassName?: string
} & Pick<MenuItemButtonSharedProps, 'start' | 'end' | 'active' | 'selected' | 'buttonRef' | 'tooltipLabel' | 'tooltipPlacement' | 'disabled' | 'draggable'>;
export declare const ToggleMenu: React.ForwardRefExoticComponent<ToggleMenuProps & React.RefAttributes<HTMLElement>>;
export {};
