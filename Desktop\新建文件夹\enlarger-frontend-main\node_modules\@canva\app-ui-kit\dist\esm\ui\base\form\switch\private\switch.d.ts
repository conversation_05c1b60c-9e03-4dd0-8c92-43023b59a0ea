import * as React from 'react';
import type { ButtonHandle } from '../../../button/button';
export type SwitchHandle = ButtonHandle;
export type SwitchProps = {
    id?: string
    value?: boolean
    defaultValue?: boolean
    disabled?: boolean
    label?: React.ReactNode
    labelWeight?: 'regular' | 'bold'
    description?: string
    ref?: React.RefObject<SwitchHandle | null>
} & SwitchA11yProps & SwitchEventHandlerProps;
type SwitchA11yProps = {
    ariaLabel?: string
    ariaLabelledBy?: string
    ariaDescribedBy?: string
};
type SwitchEventHandlerProps = {
    onChange?: (value: boolean, event?: React.MouseEvent<HTMLElement>) => void
    onFocus?: React.FocusEventHandler
    onBlur?: React.FocusEventHandler
};
export declare const Switch: React.ForwardRefExoticComponent<Omit<SwitchProps, "ref"> & React.RefAttributes<import("../../../handle/handle").BasicHTMLHandle>>;
export {};
