{"name": "@svgr/babel-plugin-transform-svg-component", "description": "Transform SVG into component", "version": "8.0.0", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "repository": "https://github.com/gregberge/svgr/tree/main/packages/babel-plugin-transform-svg-component", "author": "<PERSON> <<EMAIL>>", "publishConfig": {"access": "public"}, "keywords": ["babel-plugin"], "engines": {"node": ">=12"}, "homepage": "https://react-svgr.com", "funding": {"type": "github", "url": "https://github.com/sponsors/gregberge"}, "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "scripts": {"reset": "rm -rf dist", "build": "rollup -c ../../build/rollup.config.mjs", "prepublishOnly": "pnpm run reset && pnpm run build"}, "devDependencies": {"@types/babel__template": "^7.4.1"}, "gitHead": "52a1079681477587ef0d842c0e78531adf2d2520"}