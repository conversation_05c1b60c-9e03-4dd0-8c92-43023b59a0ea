import * as React from 'react';
import type { ClearableInputProps } from '../../clearable_input/clearable_input';
import type { PopoverProps } from '../../../surface/popover/popover';
export type SearchInputMenuHandles = {
    focus(): void;
    blur(): void;
};
export type SearchInputMenuProps = {
    children?: any;
    detachContent?: boolean;
    border?: 'solid' | 'faintShadow';
    onOutsidePointerDown?(): void;
    disableSpellcheck?: boolean;
    error?: boolean;
    usePopover?: boolean
    captureFocus?: PopoverProps['captureFocus'];
} & Omit<ClearableInputProps, 'borderless' | 'inputClassName' | 'readOnly'>;
export declare const SearchInputMenu: React.ForwardRefExoticComponent<{
    children?: any;
    detachContent?: boolean;
    border?: "solid" | "faintShadow";
    onOutsidePointerDown?(): void;
    disableSpellcheck?: boolean;
    error?: boolean;
    usePopover?: boolean
    captureFocus?: PopoverProps["captureFocus"];
} & Omit<ClearableInputProps, "readOnly" | "inputClassName" | "borderless"> & React.RefAttributes<SearchInputMenuHandles>>;
