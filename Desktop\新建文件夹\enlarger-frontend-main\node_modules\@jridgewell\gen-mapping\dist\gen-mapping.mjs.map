{"version": 3, "file": "gen-mapping.mjs", "sources": ["../src/sourcemap-segment.ts", "../src/gen-mapping.ts"], "sourcesContent": ["type GeneratedColumn = number;\ntype SourcesIndex = number;\ntype SourceLine = number;\ntype SourceColumn = number;\ntype NamesIndex = number;\n\nexport type SourceMapSegment =\n  | [GeneratedColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn, NamesIndex];\n\nexport const COLUMN = 0;\nexport const SOURCES_INDEX = 1;\nexport const SOURCE_LINE = 2;\nexport const SOURCE_COLUMN = 3;\nexport const NAMES_INDEX = 4;\n", "import { SetArray, put, remove } from '@jridgewell/set-array';\nimport { encode } from '@jridgewell/sourcemap-codec';\nimport { TraceMap, decodedMappings } from '@jridgewell/trace-mapping';\n\nimport {\n  COLUMN,\n  SOURCES_INDEX,\n  SOURCE_LINE,\n  SOURCE_COLUMN,\n  NAMES_INDEX,\n} from './sourcemap-segment';\n\nimport type { SourceMapInput } from '@jridgewell/trace-mapping';\nimport type { SourceMapSegment } from './sourcemap-segment';\nimport type { DecodedSourceMap, EncodedSourceMap, Pos, Mapping } from './types';\n\nexport type { DecodedSourceMap, EncodedSourceMap, Mapping };\n\nexport type Options = {\n  file?: string | null;\n  sourceRoot?: string | null;\n};\n\nconst NO_NAME = -1;\n\n/**\n * Provides the state to generate a sourcemap.\n */\nexport class GenMapping {\n  private declare _names: SetArray<string>;\n  private declare _sources: SetArray<string>;\n  private declare _sourcesContent: (string | null)[];\n  private declare _mappings: SourceMapSegment[][];\n  private declare _ignoreList: SetArray<number>;\n  declare file: string | null | undefined;\n  declare sourceRoot: string | null | undefined;\n\n  constructor({ file, sourceRoot }: Options = {}) {\n    this._names = new SetArray();\n    this._sources = new SetArray();\n    this._sourcesContent = [];\n    this._mappings = [];\n    this.file = file;\n    this.sourceRoot = sourceRoot;\n    this._ignoreList = new SetArray();\n  }\n}\n\ninterface PublicMap {\n  _names: GenMapping['_names'];\n  _sources: GenMapping['_sources'];\n  _sourcesContent: GenMapping['_sourcesContent'];\n  _mappings: GenMapping['_mappings'];\n  _ignoreList: GenMapping['_ignoreList'];\n}\n\n/**\n * Typescript doesn't allow friend access to private fields, so this just casts the map into a type\n * with public access modifiers.\n */\nfunction cast(map: unknown): PublicMap {\n  return map as any;\n}\n\n/**\n * A low-level API to associate a generated position with an original source position. Line and\n * column here are 0-based, unlike `addMapping`.\n */\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source?: null,\n  sourceLine?: null,\n  sourceColumn?: null,\n  name?: null,\n  content?: null,\n): void;\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source: string,\n  sourceLine: number,\n  sourceColumn: number,\n  name?: null,\n  content?: string | null,\n): void;\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source: string,\n  sourceLine: number,\n  sourceColumn: number,\n  name: string,\n  content?: string | null,\n): void;\nexport function addSegment(\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source?: string | null,\n  sourceLine?: number | null,\n  sourceColumn?: number | null,\n  name?: string | null,\n  content?: string | null,\n): void {\n  return addSegmentInternal(\n    false,\n    map,\n    genLine,\n    genColumn,\n    source,\n    sourceLine,\n    sourceColumn,\n    name,\n    content,\n  );\n}\n\n/**\n * A high-level API to associate a generated position with an original source position. Line is\n * 1-based, but column is 0-based, due to legacy behavior in `source-map` library.\n */\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source?: null;\n    original?: null;\n    name?: null;\n    content?: null;\n  },\n): void;\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source: string;\n    original: Pos;\n    name?: null;\n    content?: string | null;\n  },\n): void;\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source: string;\n    original: Pos;\n    name: string;\n    content?: string | null;\n  },\n): void;\nexport function addMapping(\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source?: string | null;\n    original?: Pos | null;\n    name?: string | null;\n    content?: string | null;\n  },\n): void {\n  return addMappingInternal(false, map, mapping as Parameters<typeof addMappingInternal>[2]);\n}\n\n/**\n * Same as `addSegment`, but will only add the segment if it generates useful information in the\n * resulting map. This only works correctly if segments are added **in order**, meaning you should\n * not add a segment with a lower generated line/column than one that came before.\n */\nexport const maybeAddSegment: typeof addSegment = (\n  map,\n  genLine,\n  genColumn,\n  source,\n  sourceLine,\n  sourceColumn,\n  name,\n  content,\n) => {\n  return addSegmentInternal(\n    true,\n    map,\n    genLine,\n    genColumn,\n    source,\n    sourceLine,\n    sourceColumn,\n    name,\n    content,\n  );\n};\n\n/**\n * Same as `addMapping`, but will only add the mapping if it generates useful information in the\n * resulting map. This only works correctly if mappings are added **in order**, meaning you should\n * not add a mapping with a lower generated line/column than one that came before.\n */\nexport const maybeAddMapping: typeof addMapping = (map, mapping) => {\n  return addMappingInternal(true, map, mapping as Parameters<typeof addMappingInternal>[2]);\n};\n\n/**\n * Adds/removes the content of the source file to the source map.\n */\nexport function setSourceContent(map: GenMapping, source: string, content: string | null): void {\n  const { _sources: sources, _sourcesContent: sourcesContent } = cast(map);\n  const index = put(sources, source);\n  sourcesContent[index] = content;\n}\n\nexport function setIgnore(map: GenMapping, source: string, ignore = true) {\n  const { _sources: sources, _sourcesContent: sourcesContent, _ignoreList: ignoreList } = cast(map);\n  const index = put(sources, source);\n  if (index === sourcesContent.length) sourcesContent[index] = null;\n  if (ignore) put(ignoreList, index);\n  else remove(ignoreList, index);\n}\n\n/**\n * Returns a sourcemap object (with decoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport function toDecodedMap(map: GenMapping): DecodedSourceMap {\n  const {\n    _mappings: mappings,\n    _sources: sources,\n    _sourcesContent: sourcesContent,\n    _names: names,\n    _ignoreList: ignoreList,\n  } = cast(map);\n  removeEmptyFinalLines(mappings);\n\n  return {\n    version: 3,\n    file: map.file || undefined,\n    names: names.array,\n    sourceRoot: map.sourceRoot || undefined,\n    sources: sources.array,\n    sourcesContent,\n    mappings,\n    ignoreList: ignoreList.array,\n  };\n}\n\n/**\n * Returns a sourcemap object (with encoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport function toEncodedMap(map: GenMapping): EncodedSourceMap {\n  const decoded = toDecodedMap(map);\n  return {\n    ...decoded,\n    mappings: encode(decoded.mappings as SourceMapSegment[][]),\n  };\n}\n\n/**\n * Constructs a new GenMapping, using the already present mappings of the input.\n */\nexport function fromMap(input: SourceMapInput): GenMapping {\n  const map = new TraceMap(input);\n  const gen = new GenMapping({ file: map.file, sourceRoot: map.sourceRoot });\n\n  putAll(cast(gen)._names, map.names);\n  putAll(cast(gen)._sources, map.sources as string[]);\n  cast(gen)._sourcesContent = map.sourcesContent || map.sources.map(() => null);\n  cast(gen)._mappings = decodedMappings(map) as GenMapping['_mappings'];\n  if (map.ignoreList) putAll(cast(gen)._ignoreList, map.ignoreList);\n\n  return gen;\n}\n\n/**\n * Returns an array of high-level mapping objects for every recorded segment, which could then be\n * passed to the `source-map` library.\n */\nexport function allMappings(map: GenMapping): Mapping[] {\n  const out: Mapping[] = [];\n  const { _mappings: mappings, _sources: sources, _names: names } = cast(map);\n\n  for (let i = 0; i < mappings.length; i++) {\n    const line = mappings[i];\n    for (let j = 0; j < line.length; j++) {\n      const seg = line[j];\n\n      const generated = { line: i + 1, column: seg[COLUMN] };\n      let source: string | undefined = undefined;\n      let original: Pos | undefined = undefined;\n      let name: string | undefined = undefined;\n\n      if (seg.length !== 1) {\n        source = sources.array[seg[SOURCES_INDEX]];\n        original = { line: seg[SOURCE_LINE] + 1, column: seg[SOURCE_COLUMN] };\n\n        if (seg.length === 5) name = names.array[seg[NAMES_INDEX]];\n      }\n\n      out.push({ generated, source, original, name } as Mapping);\n    }\n  }\n\n  return out;\n}\n\n// This split declaration is only so that terser can elminiate the static initialization block.\nfunction addSegmentInternal<S extends string | null | undefined>(\n  skipable: boolean,\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source: S,\n  sourceLine: S extends string ? number : null | undefined,\n  sourceColumn: S extends string ? number : null | undefined,\n  name: S extends string ? string | null | undefined : null | undefined,\n  content: S extends string ? string | null | undefined : null | undefined,\n): void {\n  const {\n    _mappings: mappings,\n    _sources: sources,\n    _sourcesContent: sourcesContent,\n    _names: names,\n  } = cast(map);\n  const line = getLine(mappings, genLine);\n  const index = getColumnIndex(line, genColumn);\n\n  if (!source) {\n    if (skipable && skipSourceless(line, index)) return;\n    return insert(line, index, [genColumn]);\n  }\n\n  // Sigh, TypeScript can't figure out sourceLine and sourceColumn aren't nullish if source\n  // isn't nullish.\n  assert<number>(sourceLine);\n  assert<number>(sourceColumn);\n\n  const sourcesIndex = put(sources, source);\n  const namesIndex = name ? put(names, name) : NO_NAME;\n  if (sourcesIndex === sourcesContent.length) sourcesContent[sourcesIndex] = content ?? null;\n\n  if (skipable && skipSource(line, index, sourcesIndex, sourceLine, sourceColumn, namesIndex)) {\n    return;\n  }\n\n  return insert(\n    line,\n    index,\n    name\n      ? [genColumn, sourcesIndex, sourceLine, sourceColumn, namesIndex]\n      : [genColumn, sourcesIndex, sourceLine, sourceColumn],\n  );\n}\n\nfunction assert<T>(_val: unknown): asserts _val is T {\n  // noop.\n}\n\nfunction getLine(mappings: SourceMapSegment[][], index: number): SourceMapSegment[] {\n  for (let i = mappings.length; i <= index; i++) {\n    mappings[i] = [];\n  }\n  return mappings[index];\n}\n\nfunction getColumnIndex(line: SourceMapSegment[], genColumn: number): number {\n  let index = line.length;\n  for (let i = index - 1; i >= 0; index = i--) {\n    const current = line[i];\n    if (genColumn >= current[COLUMN]) break;\n  }\n  return index;\n}\n\nfunction insert<T>(array: T[], index: number, value: T) {\n  for (let i = array.length; i > index; i--) {\n    array[i] = array[i - 1];\n  }\n  array[index] = value;\n}\n\nfunction removeEmptyFinalLines(mappings: SourceMapSegment[][]) {\n  const { length } = mappings;\n  let len = length;\n  for (let i = len - 1; i >= 0; len = i, i--) {\n    if (mappings[i].length > 0) break;\n  }\n  if (len < length) mappings.length = len;\n}\n\nfunction putAll<T extends string | number>(setarr: SetArray<T>, array: T[]) {\n  for (let i = 0; i < array.length; i++) put(setarr, array[i]);\n}\n\nfunction skipSourceless(line: SourceMapSegment[], index: number): boolean {\n  // The start of a line is already sourceless, so adding a sourceless segment to the beginning\n  // doesn't generate any useful information.\n  if (index === 0) return true;\n\n  const prev = line[index - 1];\n  // If the previous segment is also sourceless, then adding another sourceless segment doesn't\n  // genrate any new information. Else, this segment will end the source/named segment and point to\n  // a sourceless position, which is useful.\n  return prev.length === 1;\n}\n\nfunction skipSource(\n  line: SourceMapSegment[],\n  index: number,\n  sourcesIndex: number,\n  sourceLine: number,\n  sourceColumn: number,\n  namesIndex: number,\n): boolean {\n  // A source/named segment at the start of a line gives position at that genColumn\n  if (index === 0) return false;\n\n  const prev = line[index - 1];\n\n  // If the previous segment is sourceless, then we're transitioning to a source.\n  if (prev.length === 1) return false;\n\n  // If the previous segment maps to the exact same source position, then this segment doesn't\n  // provide any new position information.\n  return (\n    sourcesIndex === prev[SOURCES_INDEX] &&\n    sourceLine === prev[SOURCE_LINE] &&\n    sourceColumn === prev[SOURCE_COLUMN] &&\n    namesIndex === (prev.length === 5 ? prev[NAMES_INDEX] : NO_NAME)\n  );\n}\n\nfunction addMappingInternal<S extends string | null | undefined>(\n  skipable: boolean,\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source: S;\n    original: S extends string ? Pos : null | undefined;\n    name: S extends string ? string | null | undefined : null | undefined;\n    content: S extends string ? string | null | undefined : null | undefined;\n  },\n) {\n  const { generated, source, original, name, content } = mapping;\n  if (!source) {\n    return addSegmentInternal(\n      skipable,\n      map,\n      generated.line - 1,\n      generated.column,\n      null,\n      null,\n      null,\n      null,\n      null,\n    );\n  }\n  assert<Pos>(original);\n  return addSegmentInternal(\n    skipable,\n    map,\n    generated.line - 1,\n    generated.column,\n    source as string,\n    original.line - 1,\n    original.column,\n    name,\n    content,\n  );\n}\n"], "names": [], "mappings": ";;;;AAWO,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,MAAM,aAAa,GAAG,CAAC,CAAC;AACxB,MAAM,WAAW,GAAG,CAAC,CAAC;AACtB,MAAM,aAAa,GAAG,CAAC,CAAC;AACxB,MAAM,WAAW,GAAG,CAAC;;ACQ5B,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC;AAEnB;;AAEG;MACU,UAAU,CAAA;AASrB,IAAA,WAAA,CAAY,EAAE,IAAI,EAAE,UAAU,KAAc,EAAE,EAAA;AAC5C,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACpB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAC7B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,QAAQ,EAAE,CAAC;KACnC;AACF,CAAA;AAUD;;;AAGG;AACH,SAAS,IAAI,CAAC,GAAY,EAAA;AACxB,IAAA,OAAO,GAAU,CAAC;AACpB,CAAC;SAoCe,UAAU,CACxB,GAAe,EACf,OAAe,EACf,SAAiB,EACjB,MAAsB,EACtB,UAA0B,EAC1B,YAA4B,EAC5B,IAAoB,EACpB,OAAuB,EAAA;IAEvB,OAAO,kBAAkB,CACvB,KAAK,EACL,GAAG,EACH,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,OAAO,CACR,CAAC;AACJ,CAAC;AAoCe,SAAA,UAAU,CACxB,GAAe,EACf,OAMC,EAAA;IAED,OAAO,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,OAAmD,CAAC,CAAC;AAC7F,CAAC;AAED;;;;AAIG;MACU,eAAe,GAAsB,CAChD,GAAG,EACH,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,OAAO,KACL;IACF,OAAO,kBAAkB,CACvB,IAAI,EACJ,GAAG,EACH,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,OAAO,CACR,CAAC;AACJ,EAAE;AAEF;;;;AAIG;MACU,eAAe,GAAsB,CAAC,GAAG,EAAE,OAAO,KAAI;IACjE,OAAO,kBAAkB,CAAC,IAAI,EAAE,GAAG,EAAE,OAAmD,CAAC,CAAC;AAC5F,EAAE;AAEF;;AAEG;SACa,gBAAgB,CAAC,GAAe,EAAE,MAAc,EAAE,OAAsB,EAAA;AACtF,IAAA,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IACzE,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACnC,IAAA,cAAc,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;AAClC,CAAC;AAEK,SAAU,SAAS,CAAC,GAAe,EAAE,MAAc,EAAE,MAAM,GAAG,IAAI,EAAA;AACtE,IAAA,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IAClG,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACnC,IAAA,IAAI,KAAK,KAAK,cAAc,CAAC,MAAM;AAAE,QAAA,cAAc,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AAClE,IAAA,IAAI,MAAM;AAAE,QAAA,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;;AAC9B,QAAA,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AACjC,CAAC;AAED;;;AAGG;AACG,SAAU,YAAY,CAAC,GAAe,EAAA;IAC1C,MAAM,EACJ,SAAS,EAAE,QAAQ,EACnB,QAAQ,EAAE,OAAO,EACjB,eAAe,EAAE,cAAc,EAC/B,MAAM,EAAE,KAAK,EACb,WAAW,EAAE,UAAU,GACxB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IACd,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IAEhC,OAAO;AACL,QAAA,OAAO,EAAE,CAAC;AACV,QAAA,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,SAAS;QAC3B,KAAK,EAAE,KAAK,CAAC,KAAK;AAClB,QAAA,UAAU,EAAE,GAAG,CAAC,UAAU,IAAI,SAAS;QACvC,OAAO,EAAE,OAAO,CAAC,KAAK;QACtB,cAAc;QACd,QAAQ;QACR,UAAU,EAAE,UAAU,CAAC,KAAK;KAC7B,CAAC;AACJ,CAAC;AAED;;;AAGG;AACG,SAAU,YAAY,CAAC,GAAe,EAAA;AAC1C,IAAA,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;IAClC,OACK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,CACV,EAAA,EAAA,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,QAAgC,CAAC,EAC1D,CAAA,CAAA;AACJ,CAAC;AAED;;AAEG;AACG,SAAU,OAAO,CAAC,KAAqB,EAAA;AAC3C,IAAA,MAAM,GAAG,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;AAChC,IAAA,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,UAAU,EAAE,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC;AAE3E,IAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;AACpC,IAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,OAAmB,CAAC,CAAC;IACpD,IAAI,CAAC,GAAG,CAAC,CAAC,eAAe,GAAG,GAAG,CAAC,cAAc,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;IAC9E,IAAI,CAAC,GAAG,CAAC,CAAC,SAAS,GAAG,eAAe,CAAC,GAAG,CAA4B,CAAC;IACtE,IAAI,GAAG,CAAC,UAAU;AAAE,QAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AAElE,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;AAGG;AACG,SAAU,WAAW,CAAC,GAAe,EAAA;IACzC,MAAM,GAAG,GAAc,EAAE,CAAC;AAC1B,IAAA,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AAE5E,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,QAAA,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAEpB,YAAA,MAAM,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACvD,IAAI,MAAM,GAAuB,SAAS,CAAC;YAC3C,IAAI,QAAQ,GAAoB,SAAS,CAAC;YAC1C,IAAI,IAAI,GAAuB,SAAS,CAAC;AAEzC,YAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;gBACpB,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;AAC3C,gBAAA,QAAQ,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;AAEtE,gBAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;oBAAE,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;AAC5D,aAAA;AAED,YAAA,GAAG,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAa,CAAC,CAAC;AAC5D,SAAA;AACF,KAAA;AAED,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED;AACA,SAAS,kBAAkB,CACzB,QAAiB,EACjB,GAAe,EACf,OAAe,EACf,SAAiB,EACjB,MAAS,EACT,UAAwD,EACxD,YAA0D,EAC1D,IAAqE,EACrE,OAAwE,EAAA;IAExE,MAAM,EACJ,SAAS,EAAE,QAAQ,EACnB,QAAQ,EAAE,OAAO,EACjB,eAAe,EAAE,cAAc,EAC/B,MAAM,EAAE,KAAK,GACd,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IACd,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACxC,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAE9C,IAAI,CAAC,MAAM,EAAE;AACX,QAAA,IAAI,QAAQ,IAAI,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC;YAAE,OAAO;QACpD,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;AACzC,KAAA;IAOD,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAA,MAAM,UAAU,GAAG,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC;AACrD,IAAA,IAAI,YAAY,KAAK,cAAc,CAAC,MAAM;QAAE,cAAc,CAAC,YAAY,CAAC,GAAG,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,OAAO,GAAI,IAAI,CAAC;AAE3F,IAAA,IAAI,QAAQ,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC,EAAE;QAC3F,OAAO;AACR,KAAA;AAED,IAAA,OAAO,MAAM,CACX,IAAI,EACJ,KAAK,EACL,IAAI;UACA,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC;UAC/D,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC,CACxD,CAAC;AACJ,CAAC;AAMD,SAAS,OAAO,CAAC,QAA8B,EAAE,KAAa,EAAA;AAC5D,IAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;AAC7C,QAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAClB,KAAA;AACD,IAAA,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB,CAAC;AAED,SAAS,cAAc,CAAC,IAAwB,EAAE,SAAiB,EAAA;AACjE,IAAA,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,EAAE;AAC3C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACxB,QAAA,IAAI,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;YAAE,MAAM;AACzC,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,MAAM,CAAI,KAAU,EAAE,KAAa,EAAE,KAAQ,EAAA;AACpD,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;QACzC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACzB,KAAA;AACD,IAAA,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AACvB,CAAC;AAED,SAAS,qBAAqB,CAAC,QAA8B,EAAA;AAC3D,IAAA,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;IAC5B,IAAI,GAAG,GAAG,MAAM,CAAC;AACjB,IAAA,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC1C,QAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC;YAAE,MAAM;AACnC,KAAA;IACD,IAAI,GAAG,GAAG,MAAM;AAAE,QAAA,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC;AAC1C,CAAC;AAED,SAAS,MAAM,CAA4B,MAAmB,EAAE,KAAU,EAAA;AACxE,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE;QAAE,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED,SAAS,cAAc,CAAC,IAAwB,EAAE,KAAa,EAAA;;;IAG7D,IAAI,KAAK,KAAK,CAAC;AAAE,QAAA,OAAO,IAAI,CAAC;IAE7B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;;;;AAI7B,IAAA,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED,SAAS,UAAU,CACjB,IAAwB,EACxB,KAAa,EACb,YAAoB,EACpB,UAAkB,EAClB,YAAoB,EACpB,UAAkB,EAAA;;IAGlB,IAAI,KAAK,KAAK,CAAC;AAAE,QAAA,OAAO,KAAK,CAAC;IAE9B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;;AAG7B,IAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;AAAE,QAAA,OAAO,KAAK,CAAC;;;AAIpC,IAAA,QACE,YAAY,KAAK,IAAI,CAAC,aAAa,CAAC;AACpC,QAAA,UAAU,KAAK,IAAI,CAAC,WAAW,CAAC;AAChC,QAAA,YAAY,KAAK,IAAI,CAAC,aAAa,CAAC;QACpC,UAAU,MAAM,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,EAChE;AACJ,CAAC;AAED,SAAS,kBAAkB,CACzB,QAAiB,EACjB,GAAe,EACf,OAMC,EAAA;AAED,IAAA,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;IAC/D,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,kBAAkB,CACvB,QAAQ,EACR,GAAG,EACH,SAAS,CAAC,IAAI,GAAG,CAAC,EAClB,SAAS,CAAC,MAAM,EAChB,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL,CAAC;AACH,KAAA;AAED,IAAA,OAAO,kBAAkB,CACvB,QAAQ,EACR,GAAG,EACH,SAAS,CAAC,IAAI,GAAG,CAAC,EAClB,SAAS,CAAC,MAAM,EAChB,MAAgB,EAChB,QAAQ,CAAC,IAAI,GAAG,CAAC,EACjB,QAAQ,CAAC,MAAM,EACf,IAAI,EACJ,OAAO,CACR,CAAC;AACJ;;;;"}