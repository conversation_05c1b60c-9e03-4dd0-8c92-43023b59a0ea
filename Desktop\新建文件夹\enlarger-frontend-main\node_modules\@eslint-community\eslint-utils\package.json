{"name": "@eslint-community/eslint-utils", "version": "4.4.0", "description": "Utilities for ESLint plugins.", "keywords": ["eslint"], "homepage": "https://github.com/eslint-community/eslint-utils#readme", "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "repository": {"type": "git", "url": "https://github.com/eslint-community/eslint-utils"}, "license": "MIT", "author": "<PERSON><PERSON>", "sideEffects": false, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "main": "index", "module": "index.mjs", "files": ["index.*"], "scripts": {"prebuild": "npm run -s clean", "build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "coverage": "opener ./coverage/lcov-report/index.html", "docs:build": "vitepress build docs", "docs:watch": "vitepress dev docs", "format": "npm run -s format:prettier -- --write", "format:prettier": "prettier .", "format:check": "npm run -s format:prettier -- --check", "lint": "eslint .", "test": "c8 mocha --reporter dot \"test/*.mjs\"", "preversion": "npm test && npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "warun \"{src,test}/**/*.mjs\" -- npm run -s test:mocha"}, "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "devDependencies": {"@eslint-community/eslint-plugin-mysticatea": "^15.2.0", "c8": "^7.12.0", "dot-prop": "^6.0.1", "eslint": "^8.28.0", "mocha": "^9.2.2", "npm-run-all": "^4.1.5", "opener": "^1.5.2", "prettier": "2.8.4", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-sourcemaps": "^0.6.3", "semver": "^7.3.8", "vitepress": "^1.0.0-alpha.40", "warun": "^1.0.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}