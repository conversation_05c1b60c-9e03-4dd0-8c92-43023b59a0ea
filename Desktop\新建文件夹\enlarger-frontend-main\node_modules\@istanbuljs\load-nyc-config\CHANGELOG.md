# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [1.1.0](https://github.com/istanbuljs/load-nyc-config/compare/v1.0.0...v1.1.0) (2020-05-20)


### Features

* Create `isLoading` function ([#15](https://github.com/istanbuljs/load-nyc-config/issues/15)) ([0e58b51](https://github.com/istanbuljs/load-nyc-config/commit/0e58b516f663af7ed710ba27f2090fc28bc3fdb1))
* Support loading ES module config from `.js` files ([#14](https://github.com/istanbuljs/load-nyc-config/issues/14)) ([b1ea369](https://github.com/istanbuljs/load-nyc-config/commit/b1ea369f1e5162133b7057c5e3fefb8085671ab3))

## [1.0.0](https://github.com/istanbuljs/load-nyc-config/compare/v1.0.0-alpha.2...v1.0.0) (2019-12-20)


### Features

* Version bump only ([#11](https://github.com/istanbuljs/load-nyc-config/issues/11)) ([8c3f1be](https://github.com/istanbuljs/load-nyc-config/commit/8c3f1be8d4d30161088a79878c02210db4c2fbfb))

## [1.0.0-alpha.2](https://github.com/istanbuljs/load-nyc-config/compare/v1.0.0-alpha.1...v1.0.0-alpha.2) (2019-11-24)


### Bug Fixes

* Remove support for loading .js config under `type: 'module'` ([#10](https://github.com/istanbuljs/load-nyc-config/issues/10)) ([420fe87](https://github.com/istanbuljs/load-nyc-config/commit/420fe87da7dde3e9d98ef07f0a8a03d2b4d1dcb1))
* Resolve cwd per config that sets it ([#9](https://github.com/istanbuljs/load-nyc-config/issues/9)) ([649efdc](https://github.com/istanbuljs/load-nyc-config/commit/649efdcda405c476764eebcf15af5da542fb21e1))

## [1.0.0-alpha.1](https://github.com/istanbuljs/load-nyc-config/compare/v1.0.0-alpha.0...v1.0.0-alpha.1) (2019-10-08)


### Bug Fixes

* Add `cwd` to returned config object ([#8](https://github.com/istanbuljs/load-nyc-config/issues/8)) ([cb5184a](https://github.com/istanbuljs/load-nyc-config/commit/cb5184a))

## 1.0.0-alpha.0 (2019-10-06)


### Features

* Add support for loading config from ESM modules ([#7](https://github.com/istanbuljs/load-nyc-config/issues/7)) ([bc5ea3e](https://github.com/istanbuljs/load-nyc-config/commit/bc5ea3e)), closes [#6](https://github.com/istanbuljs/load-nyc-config/issues/6)
* Initial implementation ([ff90134](https://github.com/istanbuljs/load-nyc-config/commit/ff90134))
