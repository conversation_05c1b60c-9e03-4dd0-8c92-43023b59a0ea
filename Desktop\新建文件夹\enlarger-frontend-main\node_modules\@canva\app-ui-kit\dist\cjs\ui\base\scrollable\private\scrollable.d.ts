import * as React from 'react';
import type { BaseScrollableProps, ScrollableRef, ScrollDirection } from './base_scrollable';
import type { Indicator } from './scrollable_with_indicator';
export type ScrollableProps = Pick<BaseScrollableProps, 'children' | 'onScroll' | 'ariaLabel' | 'role'> & {
    direction?: ScrollDirection
    indicator?: Indicator
};
export declare const Scrollable: React.ForwardRefExoticComponent<Pick<BaseScrollableProps, "children" | "role" | "ariaLabel" | "onScroll"> & {
    direction?: ScrollDirection
    indicator?: Indicator
} & React.RefAttributes<ScrollableRef>>;
