import * as React from 'react';
import * as ReactDOMClient from 'react-dom/client';
import type { BoundingRect } from 'react-measure';
import type { PanRecognizer } from '../../gestures/gesture_recognizer';
import { LayerLevel } from '../../layer/layer';
import type { EaselConfiguration } from '../../provider/provider';
import type { Opacity } from './overlay';
import type { BottomSheetDragBehavior } from './sheet_behavior';
export type Role = 'complementary' | 'dialog' | 'feed' | 'grid' | 'listbox' | 'menu' | 'searchbox' | 'section' | 'tree' | 'widget';
export type Side = 'left' | 'bottom' | 'right' | 'top';
type SheetDragInternal = {
    sheetBehavior?: BottomSheetDragBehavior;
    recognizers?: PanRecognizer[];
    createHandle?: boolean;
};
export interface Sheet {
    id: string;
    from: Side;
    window: Window | undefined;
    enableAnimations: boolean;
    easelConfiguration: EaselConfiguration;
    show: boolean;
    sheetDragInternal?: SheetDragInternal;
    autoFocusOnOpen: boolean;
    outerElement: HTMLElement | undefined;
    contentElement: HTMLElement | undefined;
    parentLayerContainer?: HTMLDivElement;
    isFullscreen: boolean;
    onBottom: boolean;
    applyInsetStyles: boolean;
    roundedCorners: boolean;
    contentStyles: React.CSSProperties;
    themeClass?: string;
    props: {
        id?: string;
        role?: Role;
        ariaRoleDescription?: string;
        open?: boolean;
        forceLayer?: boolean;
        layerLevel?: LayerLevel;
        overlay?: boolean;
        overlayOpacity?: Opacity;
        closeSheetText?: string;
        sheetContainsTextInput?: boolean;
        onContentResize?(bounds: BoundingRect): void;
        onRequestClose?(context?: {
            action: 'resize' | 'backdrop';
        }): void;
        onTopOfLayerChange?(onTop: boolean): void;
        backgroundColor?: string;
        hasShadow?: boolean;
        size?: number | string;
        maxSize?: number | string;
        minSize?: number | string;
        enableOverflow?: boolean;
    } & ({
        enableLayerDraggable: false;
    } | {
        enableLayerDraggable: true;
        maxVisibleSizePx?: number
        onLayerDraggingThresholdTriggered?: (opts: {
            direction: 'opening' | 'closing';
        }) => void
    });
}
export declare class Layers {
    layers: Layer[];
    private readonly allSheets;
    addSheet(sheet: Sheet): void;
    removeSheet(sheet: Sheet): void;
    private readonly calculateNewLayerLevel;
    private readonly onLayerClose;
}
export declare class Layer {
    readonly from: Side;
    readonly forced: boolean;
    readonly argWindow: Window;
    readonly layerLevel: LayerLevel;
    sheets: Sheet[];
    current?: Sheet;
    previous?: Sheet;
    readonly element: HTMLDivElement;
    readonly root: ReactDOMClient.Root;
    currentMounted: boolean;
    readonly isHorizontal: boolean;
    constructor(from: Side, 
    forced: boolean, argWindow: Window, layerLevel?: LayerLevel);
    remove(sheet: Sheet): void;
    add(sheet: Sheet): void;
    get isOpen(): boolean;
    private update;
    get enableAnimations(): boolean;
    get hasOuterContent(): boolean;
}
export {};
