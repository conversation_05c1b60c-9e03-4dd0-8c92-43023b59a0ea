import * as React from 'react';
import type { TypographySize } from '../../typography';
export type TextPlaceholderSize = TypographySize;
export type TextPlaceholderVariant = 'regular' | 'title';
type TextPlaceholderProps = {
    size?: TextPlaceholderSize
    index?: number
    disableAnimations?: boolean
};
export declare const TextPlaceholder: React.MemoExoticComponent<(props: TextPlaceholderProps & {
    variant?: TextPlaceholderVariant
}) => import("react/jsx-runtime").JSX.Element>;
export declare const TitlePlaceholder: React.MemoExoticComponent<(props: TextPlaceholderProps) => import("react/jsx-runtime").JSX.Element>;
export {};
