export declare class Preconditions {
    static checkArgument(cond: boolean, msg?: string, ...args: any[]): asserts cond;
    static checkState(cond: boolean, msg?: string, ...args: any[]): asserts cond;
    static checkEquals<T extends string | number | boolean | symbol | null | undefined>(
     a: string | number | boolean | symbol | null | undefined,
     b: T,
     msg?: string,
     ...args: unknown[]
    ): asserts a is T;
    static checkEquals<T>(a: unknown, b: T, msg: string, ...args: unknown[]): asserts a is T;
    private static stringify;
    static checkExists(x: null | undefined, msg?: string, ...args: any[]): never;
    static checkExists<T>(x: T | null | undefined, msg?: string, ...args: any[]): T;
    private static format;
}
export declare class UnreachableError extends Error {
    constructor(x: never);
}
