import * as React from 'react';
import type { Background } from '../../../box/box';
import type { ButtonProps } from '../../../button/button';
import type { Icon } from '../../../icons/icons';
import type { Space } from '../../../metrics/metrics';
export type SurfaceHeaderProps = {
    title?: string | React.ReactNode
    titleId?: string
    description?: string | React.ReactNode
    descriptionId?: string
    alignment?: 'start' | 'center'
    start?: React.ReactNode
    end?: React.ReactNode
    divider?: boolean
    padding?: Extract<Space, '0' | '1.5u' | '2u'>
    background?: Extract<Background, 'surface'> | 'none'
};
export declare const SurfaceHeader: ({ title, titleId, description, descriptionId, alignment, start, end, divider, padding, background, }: SurfaceHeaderProps) => import("react/jsx-runtime").JSX.Element;
export declare const SurfaceHeaderTitle: ({ children, id, alignment, }: {
    children: string;
    alignment?: "start" | "center";
    id?: string;
}) => import("react/jsx-runtime").JSX.Element;
export declare const SurfaceHeaderDescription: ({ children, alignment, id, }: {
    children: string;
    alignment?: "start" | "center";
    id?: string;
}) => import("react/jsx-runtime").JSX.Element;
type SurfaceHeaderIconButtonProps = Omit<ButtonProps, 'variant' | 'icon' | 'ariaLabel' | 'size' | 'children'> & {
    icon: Icon;
    ariaLabel: string;
};
export declare const SurfaceHeaderIconButton: (props: SurfaceHeaderIconButtonProps) => import("react/jsx-runtime").JSX.Element;
type PredefinedIconButtonProps = Omit<ButtonProps, 'variant' | 'icon' | 'size' | 'children'>;
export declare const SurfaceHeaderBackButton: ({ ariaLabel, ...props }: PredefinedIconButtonProps) => import("react/jsx-runtime").JSX.Element;
export declare const SurfaceHeaderCloseButton: ({ ariaLabel, ...props }: PredefinedIconButtonProps) => import("react/jsx-runtime").JSX.Element;
type SurfaceHeaderTextButtonProps = Omit<ButtonProps, 'variant' | 'icon' | 'ariaLabel' | 'size'>;
export declare const SurfaceHeaderTextButton: (props: SurfaceHeaderTextButtonProps) => import("react/jsx-runtime").JSX.Element;
export {};
